{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}
{% with rxts=questionnaire.rxts.all %}
<p>Hi {{patient.name}},</p>
<p>This is {{doctor.name}} (CPSO #{{doctor.cpso_number}}) &amp; clinic contact phone {{doctor.phone}}.</p>
<p>I've reviewed your history. If you felt that you did not understand any questions, or felt your answers did not accurately describe or excluded symptoms, we can set up an appointment for real-time secure messaging, otherwise we can proceed with the plan below.</p>

{# --- Exposure History & Prior Doxy-PEP Use -------------------------- #}
<p class="mb-0"><strong>Exposure History and Prior Doxy-PEP Use:</strong></p>
<ul>
  <li>Exposure date / time:
    {% if data.exposure_date_time %}
      {{ data.exposure_date_time|slice:":16"|replace:"T,@ " }}
    {% else %}
      Not provided
    {% endif %}
  </li>

  {% if data.time_since_exposure %}
    <li>Hours since exposure: {{ data.time_since_exposure }}</li>
  {% endif %}

  {% if data.past_doxypep_use and data.past_doxypep_use.doxypep %}
    <li>Previous Doxy-PEP use: <strong>Yes</strong></li>

    {% if data.last_dose_doxypep %}
      <li>Last Doxy-PEP dose: {{ data.last_dose_doxypep|replace:"_, "|title }}</li>
    {% endif %}

    {% if data.doxypep_tolerance %}
      <li>Tolerance to last dose:
        {% if data.doxypep_tolerance == "none" %}
          No side effects
        {% elif data.doxypep_tolerance == "mild" %}
          Mild side effects
        {% elif data.doxypep_tolerance == "moderate_tolerable" %}
          Moderate but tolerable side effects
        {% elif data.doxypep_tolerance == "severe" %}
          Severe side effects
        {% else %}
          {{ data.doxypep_tolerance|replace:"_, "|title }}
        {% endif %}
      </li>
    {% endif %}

    {% if data.sti_test_after_doxypep %}
      <li>STI testing since last dose:
        {% if data.sti_test_after_doxypep == "tested_negative" %}
          Completed - all results negative
        {% elif data.sti_test_after_doxypep == "tested_positive" %}
          Completed - tested positive
        {% elif data.sti_test_after_doxypep == "no_testing" %}
          Not yet tested
        {% else %}
          {{ data.sti_test_after_doxypep|replace:"_, "|title }}
        {% endif %}
      </li>
    {% endif %}

  {% elif data.never_used_doxypep %}
    <li>Previous Doxy-PEP use: <strong>Never taken</strong></li>
  {% endif %}
</ul>
{# ------------------------------------------------------------------- #}



<!-- Health Condition Summary -->
<p class="mb-0"><strong>Health Condition Summary</strong></p>
<ul>
  {% with summary=questionnaire.raw_formio_summary %}

  <li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

<li><strong>Medication Allergies:</strong> 
  {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
    {{ summary.medication_allergies_list.c_val }}
  {% else %}
    None
  {% endif %}
</li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Health Conditions:</strong> 
    {% if summary.health_conditions_list and summary.health_conditions_list.c_val %}
      {{ summary.health_conditions_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  {% endwith %}
</ul>

<p class="mb-0"><strong>Doxy-PEP Treatment:</strong></p>
<ul>
  <li>You don't have any symptoms<ul>
    </ul></li>
    <li>Your exposure was within the last 72 hours</li>
    <li>You understand:<ul>
        <li>Doxy-PEP does not cure or treat chlamydia, gonorrhea or syphilis if you have had a confirmed exposure</li>
<li>Doxy-PEP is not HIV post-exposure prophylaxis (PEP), and if you wish to obtain HIV PEP you need to visit an emergency room.</li>
<li>Testing remains an important part of safe sexual health practices.</li>
    </ul></li>
</ul>

{# --- Medication Safety ------------------------------------------------ #}
<p class="mb-0"><strong>Medication Safety:</strong></p>
<ul>
  {# “Present” or “Not present” items auto-generated from Form.io summary #}
  {% for s in summary|confirm:"antibiotic_allergies,allergies_not_present,doxy_contraindications,contraindications_not_present" %}
    <li>{{ s|safe }}</li>
  {% endfor %}
</ul>

{# --- Confirmations ---------------------------------------------------- #}
<p class="mb-0"><strong>Confirmations:</strong></p>
<ul>
  {% for s in summary|confirm:"side_effects_doxy_ack,med_info_accuracy_ack,proceed_with_doxypep" %}
    <li>{{ s|safe }}</li>
  {% endfor %}
</ul>

<p class="mb-0"><strong>Plan</strong></p>
{% if insured or uninsured %} <!-- testing -->
<p class="mb-0">Lab testing for the following:</p>
{% if insured and uninsured %}
<strong class="mb-0">Insured</strong>
{% endif %}
<ul>
    {% for a in insured %}
    <li>{{a.name}} ({{a.test_type}} test)</li>
    {% endfor %}
</ul>
{% if uninsured %}
<strong class="mb-0">Uninsured</strong>
{% endif %}
<ul>
    {% for a in uninsured %}
    <li>{{a.name}} ({{a.test_type}} test)</li>
    {% endfor %}
</ul>

{% if data.recommend_any %}
<p class="mb-0">The following tests were recommended to be added to your requisition:</p>
<ul>
    {% if data.recommend_vswb %}
    <li>Vaginal self-swab<ul><li>Bacterial vaginosis (BV)</li><li>Yeast infection</li></ul></li>
    {% endif %}{% if data.recommend_uti %}
    <li>Urine culture<ul><li>Urinary tract-infection (UTI)</li></ul></li>
    {% endif %}
</ul>
{% endif %} 
{% endif %} 
{% if rxts %}
<p>Prescription for the following:</p>
<ul>
    {% for rx in rxts %}
    <li>{{rx.display_name}}</li>
    {% endfor %}
</ul>
{% endif %}
{% if data.opt_out_names %}
<p>It was noted that you opted-out of the following assays: {{data.opt_out_names}}</p>
{% endif %}

{% if data.concerned_about_known_exposure == True %}
You mentioned that you were concerned about exposure to {{data.exposure_keys|join:", "}}.
Based on your preferences I have included prescription treatments in your plan.
{% endif %}
<p class="mb-0">By continuing with the plan you confirm:</p>
<ul>
    <li>You don't have any abdominal/pelvic pain or fevers/chills<ul>
        <li>You'll seek same-day follow-up care in-person if you develop any</li>
    </ul></li>
    <li>Your medical history is correct</li>
    <li>You understand:<ul>
        <li>All advice that was provided in the intake assessment</li>
        <li>Accurate testing requires waiting a certain time after the exposure date (window period)</li>
    </ul></li>
</ul>
<!-- <p><em>Note that your history questionnaire, window period, and advice information is below for your reference.</em></p> -->
<p>Best regards,<br>{{doctor.name}}</p>
<hr>
<p class="mb-0"><strong>Medical History Summary:</strong></p>
<ul>
{% for s in summary|confirm:"sex, chief_complaint,testing_indication,last_sti_test, partner_sex, type_of_sex, hep_b_vaccinated, syphilis_last_treatment, last_rpr_titre_level, currently_pregnant" %}
<li>{{s|safe}}</li>
{% endfor %}
</ul>


<p class="mb-0">Symptoms:</p>
<ul>
{% for s in summary|confirm:"vaginal_symptoms_present,urinary_symptoms_present,vaginal_symptoms_not_present,urinary_symptoms_not_present,std_red_flag_symptoms_not_present" %}
<li>{{s|safe}}</li>
{% endfor %}
</ul>
<p class="mb-0">Window Periods:</p>
<ul>
{% for s in summary|confirm:"exposure_date, not_in_early_window_period, not_in_late_window_period" %}
<li>{{s|safe}}</li>
{% endfor %}
</ul>
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
