{"components": [{"key": "heading_asthma_symptoms", "html": "<h2 class='text-center'>Asthma Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "changes_urinary", "type": "radio", "input": true, "label": "Have you noticed any of the following urinary symptoms?<ul><li>Burning, stinging, or discomfort while urinating</li><li>Frequent urination, urgency, or bladder pressure</li><li>side pain, visible blood in urine, fever or chills</li><li>(If Male)Penile discharge or scrotal pain</li></ul>", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "urinary_symptom_overview", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Burning, discomfort or tingling while urinating", "value": "dysuria"}, {"label": "Frequent urination", "value": "frequency"}, {"label": "Urgent need to urinate", "value": "urgency"}, {"label": "Lower abdominal pressure or pain", "value": "bladder_pain"}, {"label": "Blood in urine", "value": "hematuria"}, {"label": "Fever or chills", "value": "fever"}, {"label": "Pain in the side or back (side pain)", "value": "side_pain"}, {"label": "Discharge", "value": "penile_discharge", "customConditional": "show = data.sex === 'male'"}, {"label": "Testicular or scrotal pain", "value": "testicular_pain", "customConditional": "show = data.sex === 'male'"}, {"label": "Urinary odour", "value": "odour"}, {"label": "Other urinary/genital symptoms", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_urinary_symptom_overview", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_urinary_symptom_overview || _.some(_.values(data.urinary_symptom_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.changes_urinary"}, {"key": "urinary_symptoms_present", "type": "textfield", "input": true, "label": "Patient indicated they have the following urinary symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You have the following urinary symptoms:", "calculateValue": "value = _.join(_.map(_.filter([ { label: 'Burning, discomfort or tingling while urinating', value: 'dysuria' }, { label: 'Frequent urination', value: 'frequency' }, { label: 'Urgent need to urinate', value: 'urgency' }, { label: 'Lower abdominal pressure or pain', value: 'bladder_pain' }, { label: 'Blood in urine', value: 'hematuria' }, { label: 'Fever or chills', value: 'fever' }, { label: 'Pain in the side or back (side pain)', value: 'side_pain' }, { label: 'Discharge', value: 'penile_discharge' }, { label: 'Testicular or scrotal pain', value: 'testicular_pain' }, { label: 'Urinary odour', value: 'odour' }, { label: 'Other urinary/genital symptoms', value: 'other' } ], function(option) { return data.urinary_symptom_overview && data.urinary_symptom_overview[option.value]; }), 'label'), ', ');"}, {"key": "urinary_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following urinary symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You DO NOT have the following urinary symptoms:", "calculateValue": "value = _.join(_.map(_.filter([ { label: 'Burning, discomfort or tingling while urinating', value: 'dysuria' }, { label: 'Frequent urination', value: 'frequency' }, { label: 'Urgent need to urinate', value: 'urgency' }, { label: 'Lower abdominal pressure or pain', value: 'bladder_pain' }, { label: 'Blood in urine', value: 'hematuria' }, { label: 'Fever or chills', value: 'fever' }, { label: 'Pain in the side or back (side pain)', value: 'side_pain' }, { label: 'Discharge', value: 'penile_discharge' }, { label: 'Testicular or scrotal pain', value: 'testicular_pain' }, { label: 'Urinary odour', value: 'odour' } ], function(option) { return !(data.urinary_symptom_overview && data.urinary_symptom_overview[option.value]); }), 'label'), ', ');"}, {"key": "stated_other_symptoms_urinary", "type": "textarea", "input": true, "label": "Please use this area to describe your other urinary symptoms not described above.<br>Please leave this section blank if you are asymptomatic or have no symptoms.", "adminFlag": true, "tableView": true, "autoExpand": false, "placeholder": "No symptoms", "customConditional": "show = data.urinary_symptom_overview?.other===true"}, {"key": "urinary_symptom_contradiction_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Notice:</strong> You indicated that you have urinary symptoms, but then selected \"None of the above.\" Please review your answers above and make sure they reflect your current symptoms accurately.</div>", "type": "content", "input": false, "customConditional": "show = data.changes_urinary === 'yes' && data.none_of_the_above_urinary_symptom_overview === true"}, {"key": "urinary_symptom_triggers", "type": "selectboxes", "input": true, "label": "Did these symptoms start after any of the following triggers? Please select all that apply:", "values": [{"label": "New lubricant during intercourse", "value": "new_lubricant", "shortcut": ""}, {"label": "Use of a sex toy", "value": "sex_toy", "shortcut": ""}, {"label": "New hygiene product (e.g., urinary soap)", "value": "new_hygiene_product", "shortcut": ""}, {"label": "Douching", "value": "douching", "shortcut": ""}, {"label": "Exposure to a new condom brand", "value": "new_condom_brand", "shortcut": ""}, {"label": "New sexual partner", "value": "new_sexual_partner", "shortcut": ""}, {"label": "Particular fabric or underwear", "value": "fabric", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.urinary_symptom_overview && _.some(_.values(data.urinary_symptom_overview)) && !data.none_of_the_above_urinary_symptom_overview", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_urinary_symptom_triggers", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a trigger."}, "validate": {"custom": "valid = !!data.none_of_the_above_urinary_symptom_triggers || _.some(_.values(data.urinary_symptom_triggers));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.urinary_symptom_overview && _.some(_.values(data.urinary_symptom_overview)) && !data.none_of_the_above_urinary_symptom_overview"}, {"key": "urinary_symptom_triggers_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following urinary symptom triggers:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following urinary symptom triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.urinary_symptom_triggers, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "urinary_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your urinary symptoms start around the same time or over separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview && _.sum(_.values(data.urinary_symptom_overview).map(Number)) >= 2", "optionsLabelPosition": "right"}, {"key": "durations_urinary", "type": "textfield", "input": true, "label": "Durations:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, {"key": "all_urinary_symptoms_start", "data": {"custom": "values = data.durations_urinary"}, "type": "select", "input": true, "label": "When did your urinary symptoms start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview && data.urinary_symptom_onset_pattern == 'same_time' && !data.no_urinary_symptoms", "optionsLabelPosition": "right"}, {"key": "heading_urinary_dysuria", "html": "<h4 class='mb-n2'>Burning, Discomfort or Tingling while Urinating</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.urinary_symptom_overview && data.urinary_symptom_overview.dysuria === true"}, {"key": "symptom_start_urinary_dysuria", "data": {"custom": "values = [{label: `I've always had discomfort/tingling with urination and this isn't new for me`,value:`always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did your discomfort with urination start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.urinary_symptom_overview && data.urinary_symptom_overview.dysuria && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "urinary_dysuria_quality", "type": "selectboxes", "input": true, "label": "How would you describe the discomfort while urinating? Please select all that apply:", "values": [{"label": "Burning sensation", "value": "burning"}, {"label": "Stinging", "value": "stinging"}, {"label": "Sharp pain at the start of urination", "value": "start_pain"}, {"label": "Sharp pain at the end of urination", "value": "end_pain"}, {"label": "Tingling or itching", "value": "tingling"}, {"label": "Feeling of incomplete emptying", "value": "incomplete_emptying"}, {"label": "Discomfort in the urethra (urine tube)", "value": "urethral_discomfort"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.dysuria === true", "optionsLabelPosition": "right"}, {"key": "dysuria_new_or_recurrent", "type": "radio", "input": true, "label": "Is this an entirely new issue for you, or something you've experienced on and off in the past?", "values": [{"label": "This is completely new for me", "value": "new"}, {"label": "I've had this on and off in the past", "value": "recurrent"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.dysuria === true", "optionsLabelPosition": "right"}, {"key": "dysuria_prior_investigation_methods", "type": "selectboxes", "input": true, "label": "What type of testing or evaluation have you had in the past for this symptom?", "values": [{"label": "Urine test or lab testing (e.g. urinalysis, culture)", "value": "lab_testing"}, {"label": "Ultrasound or other imaging", "value": "imaging"}, {"label": "Physical exam by a doctor", "value": "exam"}, {"label": "Other type of evaluation", "value": "other"}, {"label": "I don't know / I don't remember", "value": "unknown"}], "tableView": true, "confirm_label": "You have had the following prior evaluation for discomfort with urination:", "customConditional": "show = data.dysuria_new_or_recurrent === 'recurrent'", "optionsLabelPosition": "right"}, {"key": "dysuria_prior_investigation_other", "type": "textarea", "input": true, "label": "Please describe the other type of evaluation you had:", "tableView": true, "autoExpand": false, "placeholder": "e.g. CT scan, referral to urologist, cystoscopy", "customConditional": "show = data.dysuria_prior_investigation_methods?.other === true"}, {"key": "dysuria_investigation_results_recall", "type": "radio", "input": true, "label": "Do you remember if anything abnormal was found on previous tests or imaging?", "values": [{"label": "Yes - something abnormal was found", "value": "abnormal"}, {"label": "No - I was told everything was normal", "value": "normal"}, {"label": "I don't remember / I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.dysuria_new_or_recurrent === 'recurrent'", "optionsLabelPosition": "right"}, {"key": "dysuria_abnormal_findings_description", "type": "textarea", "input": true, "label": "Do you remember what abnormality was found or what your doctor told you about the results?", "tableView": true, "autoExpand": false, "placeholder": "e.g. urinary tract infection, kidney stone, inflammation, blood in urine", "customConditional": "show = data.dysuria_investigation_results_recall === 'abnormal'"}, {"key": "heading_frequency", "html": "<h4 class='mb-n2'>Urinary Frequency</h4>", "type": "content", "input": false, "label": "Urinary Frequency Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.urinary_symptom_overview && data.urinary_symptom_overview.frequency === true"}, {"key": "symptom_start_frequency", "data": {"custom": "values = [{label: `I've always had frequent urination and this isn't new for me`, value: `always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did you start experiencing frequent urination?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.frequency && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "heading_urinary_urgency", "html": "<h4 class='mb-n2'>Urinary Urgency</h4>", "type": "content", "input": false, "label": "Urgency Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.urinary_symptom_overview && data.urinary_symptom_overview.urgency === true"}, {"key": "symptom_start_urgency", "data": {"custom": "values = [{label: `I've always had urinary urgency and this isn't new for me`, value: `always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did you start experiencing urgency to urinate?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.urgency && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "urinary_urgency_pattern", "type": "radio", "input": true, "label": "When you feel the urge to urinate, how quickly do you feel you need to go?", "values": [{"label": "I need to urinate immediately and can't delay", "value": "immediate"}, {"label": "I can hold it for a few minutes but it's uncomfortable", "value": "can_hold"}, {"label": "It just feels more frequent than usual, but not urgent", "value": "mild"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.urgency || data.urinary_symptom_overview?.frequency", "optionsLabelPosition": "right"}, {"key": "urinary_nighttime_waking", "type": "radio", "input": true, "label": "Do you frequently wake up during the night to urinate?", "values": [{"label": "Yes, usually 1 or more times per night", "value": "yes"}, {"label": "No, I don't wake up to urinate", "value": "no"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.urgency || data.urinary_symptom_overview?.frequency", "optionsLabelPosition": "right"}, {"key": "urinary_nighttime_pattern_timing", "type": "radio", "input": true, "label": "Is waking at night to urinate something new for you, or something you've experienced for a long time?", "values": [{"label": "This is a new change", "value": "new"}, {"label": "It's been happening on and off for years", "value": "longstanding"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_nighttime_waking === 'yes' || data.urinary_nighttime_waking === 'other'", "optionsLabelPosition": "right"}, {"key": "urinary_frequency_urgency_risk_factors", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you? These can contribute to frequent or urgent urination:", "values": [{"label": "Drinking lots of fluids before bedtime", "value": "fluid_intake"}, {"label": "Caffeinated beverages (coffee, tea, energy drinks)", "value": "caffeine"}, {"label": "Carbonated or acidic beverages (e.g. soda, citrus juice)", "value": "acidic_beverages"}, {"label": "Recent increase in alcohol intake", "value": "alcohol"}, {"label": "Anxiety or stress", "value": "stress"}, {"label": "Other factors", "value": "other"}, {"label": "None of the above", "value": "none"}], "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.urgency || data.urinary_symptom_overview?.frequency", "optionsLabelPosition": "right"}, {"key": "urinary_frequency_urgency_risk_other_description", "type": "textarea", "input": true, "label": "Please describe the other factor(s) you think may contribute to your urinary frequency or urgency:", "tableView": true, "autoExpand": false, "placeholder": "e.g. spicy food, certain medications, etc.", "customConditional": "show = data.urinary_frequency_urgency_risk_factors?.other === true"}, {"key": "urinary_risk_correlation_with_symptoms", "type": "radio", "input": true, "label": "Do you notice a correlation between any of these triggers and your urinary symptoms?", "values": [{"label": "Yes, the symptoms worsen with certain triggers", "value": "yes"}, {"label": "No, I haven't noticed a pattern", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = _.some(_.values(data.urinary_frequency_urgency_risk_factors).slice(0, -1))", "optionsLabelPosition": "right"}, {"key": "urinary_symptom_resolution_with_avoidance", "type": "radio", "input": true, "label": "Do your symptoms improve when you avoid or limit these triggers?", "values": [{"label": "Yes, they go away or improve noticeably", "value": "yes"}, {"label": "No, the symptoms persist even if I avoid triggers", "value": "no"}, {"label": "I haven't tried limiting them", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_risk_correlation_with_symptoms === 'yes'", "optionsLabelPosition": "right"}, {"key": "heading_bladder_pain", "html": "<h4 class='mb-n2'>Bladder Pressure or Lower Abdominal Pain</h4>", "type": "content", "input": false, "label": "Bladder Pain Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.urinary_symptom_overview && data.urinary_symptom_overview.bladder_pain === true"}, {"key": "symptom_start_bladder_pain", "data": {"custom": "values = [{label: `I've always had bladder discomfort or pressure and this isn't new for me`, value: `always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did your bladder pressure or lower abdominal pain start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.bladder_pain && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "bladder_pain_location_description", "type": "selectboxes", "input": true, "label": "Where are you feeling the pressure or pain? Please select all that apply:", "values": [{"label": "In the middle of my lower stomach (just <strong>below the belly button</strong>)", "value": "midline_lower_belly"}, {"label": "On the right side of my lower stomach", "value": "right_lower_belly"}, {"label": "On the left side of my lower stomach", "value": "left_lower_belly"}, {"label": "In my lower back or tailbone", "value": "low_back"}, {"label": "Deep inside, low in my bottom", "value": "rectal"}, {"label": "Between my genitals and my buttock (i.e. bum)", "value": "between_front_and_back"}, {"label": "The pain spreads to my testicles or scrotum", "value": "radiates_to_testicles", "customConditional": "show = data.sex === 'male'"}, {"label": "The pain spreads into my groin or upper thighs", "value": "radiates_to_groin"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.bladder_pain === true", "optionsLabelPosition": "right"}, {"key": "bladder_pain_location_other_description", "type": "textarea", "input": true, "label": "Please describe the area where you're feeling pain or pressure:", "tableView": true, "autoExpand": false, "placeholder": "e.g. around my hips, just under my ribs, on one side only...", "customConditional": "show = data.bladder_pain_location_description?.other === true"}, {"key": "bladder_pain_quality", "type": "selectboxes", "input": true, "label": "How would you describe the pain or pressure in your bladder? (Select all that apply)", "values": [{"label": "Dull ache", "value": "dull_ache"}, {"label": "Sharp or stabbing", "value": "sharp"}, {"label": "Pressure or heaviness", "value": "pressure"}, {"label": "Burning sensation", "value": "burning"}, {"label": "Cramping", "value": "cramping"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.bladder_pain === true", "optionsLabelPosition": "right"}, {"key": "bladder_pain_triggers", "type": "selectboxes", "input": true, "label": "When do you usually notice the bladder pain or pressure? Please select all that apply:", "values": [{"label": "Present all the time", "value": "constant"}, {"label": "Before I urinate", "value": "before_urination"}, {"label": "While I am urinating", "value": "during_urination"}, {"label": "Right after I urinate", "value": "after_urination"}, {"label": "When my bladder feels full", "value": "bladder_fullness"}, {"label": "When I sit down or bend forward", "value": "position_pressure"}, {"label": "Randomly - no clear pattern", "value": "random"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.bladder_pain === true", "optionsLabelPosition": "right"}, {"key": "bladder_pain_triggers_other_description", "type": "textarea", "input": true, "label": "Please describe when you notice the pain or pressure if not listed above:", "tableView": true, "autoExpand": false, "placeholder": "e.g. during exercise, after sex, after certain foods...", "customConditional": "show = data.bladder_pain_triggers?.other === true"}, {"key": "heading_hematuria", "html": "<h4 class='mb-n2'>Blood in Urine</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.urinary_symptom_overview && data.urinary_symptom_overview.hematuria === true"}, {"key": "symptom_start_hematuria", "data": {"custom": "values = [{label: `I've always had blood in my urine and this isn't new for me`, value: `always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did you first notice blood in your urine?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.hematuria && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "hematuria_timing_pattern", "type": "radio", "input": true, "label": "When do you usually see blood in your urine?", "values": [{"label": "At the very beginning when I start to urinate", "value": "initial"}, {"label": "At the end of urination", "value": "end"}, {"label": "The entire time I'm urinating", "value": "throughout"}, {"label": "Only once or twice, not every time I pee", "value": "intermittent"}, {"label": "It shows up on toilet paper but not in the toilet water", "value": "toilet_paper_only"}, {"label": "I just saw a pinkish tinge / not bright red", "value": "pink"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.hematuria === true", "optionsLabelPosition": "right"}, {"key": "hematuria_appearance_description", "type": "selectboxes", "input": true, "label": "How would you describe the blood in your urine?", "values": [{"label": "Bright red", "value": "bright_red"}, {"label": "Brown or rust-coloured", "value": "rust_coloured"}, {"label": "Pink or light red", "value": "pink"}, {"label": "Clots of blood (small or large)", "value": "clots"}, {"label": "Mixed evenly with the urine (not just streaks)", "value": "diffuse"}, {"label": "Just a drop or two in the toilet", "value": "trace"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.hematuria === true", "optionsLabelPosition": "right"}, {"key": "hematuria_typical_of_past", "type": "radio", "input": true, "label": "Does this episode of blood in your urine feel similar to how past UTIs have looked or felt?", "values": [{"label": "Yes, this happens when I get UTIs", "value": "yes"}, {"label": "No, this is new or unusual for me", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.hematuria === true", "optionsLabelPosition": "right"}, {"key": "heading_urinary_fever", "html": "<h4 class='mb-n2'>Fever or Chills</h4>", "type": "content", "input": false, "label": "Fever Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.urinary_symptom_overview && data.urinary_symptom_overview.fever === true"}, {"key": "symptom_start_fever", "data": {"custom": "values = [{label: `I've always experienced fever or chills with urinary symptoms`, value: `always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did your fever or chills begin?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.fever && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "fever_measured_or_felt", "type": "radio", "input": true, "label": "Did you measure your fever with a thermometer, or did you just feel like you had a fever?", "values": [{"label": "I measured it with a thermometer", "value": "measured"}, {"label": "I just felt hot, sweaty or chilled but didn't take my temperature", "value": "felt_only"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.fever === true", "optionsLabelPosition": "right"}, {"key": "fever_temp_over_38", "type": "radio", "input": true, "label": "Was your temperature 38°C (100.4°F) or higher?", "values": [{"label": "Yes, it was 38°C / 100.4°F or more", "value": "yes"}, {"label": "No, it was lower than 38°C / 100.4°F", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.fever_measured_or_felt === 'measured'", "optionsLabelPosition": "right"}, {"key": "fever_with_cold_symptoms", "type": "selectboxes", "input": true, "label": "When the fever or chills started, did you also have any of the following symptoms? Please select all that apply:", "values": [{"label": "Runny or stuffy nose", "value": "runny_nose"}, {"label": "Sore throat", "value": "sore_throat"}, {"label": "<PERSON><PERSON>", "value": "cough"}, {"label": "Muscle aches or body fatigue", "value": "body_aches"}, {"label": "Other cold or flu symptoms", "value": "other"}], "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.fever === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_fever_with_cold_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_fever_with_cold_symptoms || _.some(_.values(data.fever_with_cold_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.urinary_symptom_overview?.fever === true"}, {"key": "fever_with_cold_symptoms_other_description", "type": "textarea", "input": true, "label": "Please describe the other cold or flu symptom you experienced:", "tableView": true, "autoExpand": false, "placeholder": "e.g. sneezing, chills, headache, sinus pressure...", "customConditional": "show = data.fever_with_cold_symptoms?.other === true"}, {"key": "fever_with_cold_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cold/flu symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No cold/flu symptoms reported at time of fever:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.fever_with_cold_symptoms, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_side_pain", "html": "</br><h4 class='mb-n2'>Side or Back Pain</h4>", "type": "content", "input": false, "label": "Side Pain Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.urinary_symptom_overview && data.urinary_symptom_overview.side_pain === true"}, {"key": "symptom_start_side_pain", "data": {"custom": "values = [{label: `I've always had side/back pain and this isn't new for me`, value: `always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did your back/side pain start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.side_pain && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "side_pain_character", "type": "selectboxes", "input": true, "label": "How would you describe the pain? Please select all that apply:", "values": [{"label": "Sharp or stabbing", "value": "sharp"}, {"label": "Dull or aching", "value": "dull"}, {"label": "Comes in waves (on and off, with strong peaks)", "value": "colicky"}, {"label": "Feels like a muscle spasm or strain", "value": "muscle_strain"}, {"label": "Worse when I twist, lift, or move", "value": "movement_triggered"}, {"label": "Pain wraps around from my back to the front", "value": "radiates_forward"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.side_pain === true", "optionsLabelPosition": "right"}, {"key": "side_pain_character_other", "type": "textarea", "input": true, "label": "Please describe the pain in your own words:", "tableView": true, "autoExpand": false, "placeholder": "e.g. throbbing, pressure-like, tightness, etc.", "customConditional": "show = data.side_pain_character?.other === true"}, {"key": "side_pain_prior_history", "type": "radio", "input": true, "label": "Is this the first time you've had this kind of side/back pain?", "values": [{"label": "Yes, this is completely new", "value": "new"}, {"label": "No, this feels similar to pain I've had before", "value": "recurrence"}, {"label": "This is a flare-up of an ongoing issue", "value": "flare"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.side_pain === true", "optionsLabelPosition": "right"}, {"key": "side_pain_prior_diagnosis_list", "type": "selectboxes", "input": true, "label": "If a doctor assessed you for this type of pain in the past, do you remember what they said it was?", "values": [{"label": "Kidney stone", "value": "stone"}, {"label": "Muscle strain or mechanical back pain", "value": "mechanical_back"}, {"label": "Kidney infection or UTI", "value": "kidney_infection"}, {"label": "Spine or nerve issue (e.g. sciatica, herniated disc)", "value": "spine_nerve"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.side_pain_prior_history === 'recurrence'", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_side_pain_prior_diagnosis", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one diagnosis or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_side_pain_prior_diagnosis || _.some(_.values(data.side_pain_prior_diagnosis_list));"}, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.side_pain_prior_history === 'recurrence'"}, {"key": "side_pain_similarity_to_prior_diagnosis", "type": "radio", "input": true, "label": "Does your current pain feel similar to what you experienced during that past diagnosis?", "values": [{"label": "Yes, it feels exactly the same", "value": "identical"}, {"label": "It feels a little similar, but not quite the same", "value": "somewhat"}, {"label": "No, it feels different this time", "value": "different"}, {"label": "I don't remember well enough to say", "value": "uncertain"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.side_pain_prior_history === 'recurrence'", "optionsLabelPosition": "right"}, {"key": "side_pain_recent_trauma", "type": "radio", "input": true, "label": "Did this pain start after a specific activity or injury?", "values": [{"label": "Yes - I lifted something heavy, worked out, or was injured", "value": "yes"}, {"label": "No - it started on its own", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.side_pain === true", "optionsLabelPosition": "right"}, {"key": "side_pain_preceded_other_symptoms", "type": "radio", "input": true, "label": "Did this side or back pain start before your other urinary symptoms (e.g. burning, urgency, or fever)?", "values": [{"label": "Yes, the side/back pain started first", "value": "yes"}, {"label": "No, the other symptoms started first", "value": "no"}, {"label": "They started at the same time", "value": "same_time"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.side_pain_prior_history === 'recurrence' && _.some(_.values(data.urinary_symptom_overview))", "optionsLabelPosition": "right"}, {"key": "heading_penile_discharge", "html": "</br><h4 class='mb-n2'>Penile Discharge</h4>", "type": "content", "input": false, "label": "Discharge Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview && data.urinary_symptom_overview.penile_discharge === true"}, {"key": "symptom_start_penile_discharge", "data": {"custom": "values = [{label: `I've always had penile discharge and this isn't new for me`, value: `always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did you first notice penile discharge?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "penile_discharge_description", "type": "selectboxes", "input": true, "label": "How would you describe the discharge? Please select all that apply:", "values": [{"label": "Clear", "value": "clear"}, {"label": "White", "value": "white"}, {"label": "Yellow", "value": "yellow"}, {"label": "Green", "value": "green"}, {"label": "Bloody or streaked with blood", "value": "bloody"}, {"label": "Thick or sticky", "value": "thick"}, {"label": "Thin or watery", "value": "thin"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge === true", "optionsLabelPosition": "right"}, {"key": "penile_discharge_other_description", "type": "textarea", "input": true, "label": "Please describe the discharge:", "tableView": true, "autoExpand": false, "placeholder": "e.g. cloudy, foul-smelling, etc.", "customConditional": "show = data.penile_discharge_description?.other === true"}, {"key": "penile_discharge_frequency", "type": "radio", "input": true, "label": "Is the discharge present all the time or only sometimes?", "values": [{"label": "It's present all the time", "value": "constant"}, {"label": "It only happens occasionally", "value": "intermittent"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge === true", "optionsLabelPosition": "right"}, {"key": "penile_discharge_expressible", "type": "radio", "input": true, "label": "If you gently press or squeeze the tip of your penis, does more discharge come out?", "values": [{"label": "Yes, I can express more discharge", "value": "yes"}, {"label": "No, it doesn't seem to come out that way", "value": "no"}, {"label": "I haven't tried", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge === true", "optionsLabelPosition": "right"}, {"key": "history_yeast_penile", "type": "radio", "input": true, "label": "Have you ever been told you had a yeast infection on the foreskin or head of the penis (balanitis)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge === true", "optionsLabelPosition": "right"}, {"key": "balanitis_symptoms", "type": "selectboxes", "input": true, "label": "Are you noticing any of the following symptoms on the head of the penis or under the foreskin?", "values": [{"label": "Red dots or rash", "value": "red_dots"}, {"label": "White buildup or coating", "value": "white_buildup"}, {"label": "Peeling, flaky, or cracked skin", "value": "peeling_skin"}, {"label": "Soreness or burning", "value": "soreness"}, {"label": "Itchiness", "value": "itch"}, {"label": "None of the above", "value": "none"}], "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge === true", "optionsLabelPosition": "right"}, {"key": "penile_discharge_treatment_to_date", "type": "textarea", "input": true, "label": "Have you tried any treatment for this discharge or skin irritation?", "tableView": true, "autoExpand": false, "placeholder": "e.g. over-the-counter creams, prescription medication, no treatment yet...", "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge === true"}, {"key": "penile_balanitis_otc_recommendation", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>What is balanitis?</strong><br>Balanitis is a common skin condition that affects the head of the penis and foreskin (if uncircumcised). It can be caused by irritation from soaps, lubricants, laundry detergents, oral sex, or tight clothing.<br><br>Common symptoms include itchiness, redness or sometimes small red dots on the head, white buildup, or peeling skin with an odour. Balanitis is usually not serious and doesn't typically cause blisters or open sores.<br><br><strong>Recommendation:</strong><br>If you have signs of balanitis, you can try an over-the-counter antifungal cream such as <strong>Clotrimazole 1%</strong> (the same ingredient used for athlete's foot or jock itch).<br><br><strong>Apply a thin layer to the head of the penis and under the foreskin (if present), twice a day for 7-10 days.</strong><br><br>While treating, avoid further irritation by skipping scented soaps or body washes, avoiding new lubricants, and wearing loose, breathable underwear.<br><br><strong>Important:</strong> If your symptoms return or don't improve, you will require a doctor's exam.</div>", "type": "content", "input": false, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge === true && ['itch','red_dots','white_buildup'].some(k => data.balanitis_symptoms?.[k])"}, {"key": "penile_balanitis_otc_recommendation_understanding", "type": "radio", "input": true, "label": "Do you understand this treatment recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.penile_discharge === true && ['red_dots','white_buildup','peeling_skin','itch','soreness'].some(k => data.balanitis_symptoms?.[k])", "optionsLabelPosition": "right"}, {"key": "heading_testicular_pain", "html": "</br><h4 class='mb-n2'>Testicular or Scrotal Pain</h4>", "type": "content", "input": false, "label": "Scrotal Pain Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview && data.urinary_symptom_overview.testicular_pain === true"}, {"key": "symptom_start_testicular_pain", "data": {"custom": "values = [{label: `I've always had testicular or scrotal pain and this isn't new for me`, value: `always`}].concat(data.durations_urinary)"}, "type": "select", "input": true, "label": "When did your testicular or scrotal pain begin?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "testicular_side_affected", "type": "radio", "input": true, "label": "Which testicle is affected?", "values": [{"label": "Right", "value": "right"}, {"label": "Left", "value": "left"}, {"label": "Both", "value": "both"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain === true", "optionsLabelPosition": "right"}, {"key": "testicular_prior_diagnosis", "type": "selectboxes", "input": true, "label": "Have you ever been told you had any of the following related to your testicles or groin?", "values": [{"label": "Groin or inguinal hernia", "value": "hernia"}, {"label": "Torsion (twisted testicle)", "value": "torsion"}, {"label": "Epididymitis (infection or inflammation)", "value": "epididymitis"}, {"label": "Varicocele (enlarged veins around testicle)", "value": "varicocele"}, {"label": "Other condition", "value": "other"}], "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_testicular_prior_diagnosis", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_testicular_prior_diagnosis || _.some(_.values(data.testicular_prior_diagnosis));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain === true"}, {"key": "testicular_prior_diagnosis_other_description", "type": "textarea", "input": true, "label": "Please describe the other condition you were diagnosed with:", "tableView": true, "autoExpand": false, "placeholder": "e.g. hydrocele, testicular cyst, etc.", "customConditional": "show = data.testicular_prior_diagnosis?.other === true"}, {"key": "testicular_lifting_relieves_pain", "type": "radio", "input": true, "label": "Does the pain improve if you lift or support the testicle (e.g. while lying down or wearing tighter underwear)?", "values": [{"label": "Yes, lifting helps reduce the pain", "value": "yes"}, {"label": "No, it doesn't help", "value": "no"}, {"label": "Not sure / haven't tried", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain === true", "optionsLabelPosition": "right"}, {"key": "testicular_felt_lump", "type": "radio", "input": true, "label": "Have you noticed a lump or bump in your testicle or scrotum?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain === true", "optionsLabelPosition": "right"}, {"key": "testicular_recent_trauma", "type": "radio", "input": true, "label": "Have you had any recent injury or trauma to your testicles?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain === true", "optionsLabelPosition": "right"}, {"key": "testicular_pain_progression", "type": "radio", "input": true, "label": "Since the pain started, is it getting better, worse, or staying the same?", "values": [{"label": "Getting better", "value": "better"}, {"label": "Getting worse", "value": "worse"}, {"label": "Staying about the same", "value": "same"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain === true", "optionsLabelPosition": "right"}, {"key": "testicular_investigated_prior", "type": "radio", "input": true, "label": "Have you ever had this testicle examined in the past with a physical exam or ultrasound?", "values": [{"label": "Yes, I had an exam and/or an ultrasound", "value": "yes"}, {"label": "No, I've never had it checked before", "value": "no"}, {"label": "I don't remember", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'male' && data.urinary_symptom_overview?.testicular_pain === true", "optionsLabelPosition": "right"}, {"key": "testicular_investigated_prior_diagnosis", "type": "selectboxes", "input": true, "label": "What were you diagnosed with after that exam or ultrasound? Please select all that apply:", "values": [{"label": "Hernia or bulge in the groin", "value": "hernia"}, {"label": "Torsion (twisted testicle)", "value": "torsion"}, {"label": "Epididymitis (inflammation or infection)", "value": "epididymitis"}, {"label": "Varicocele (enlarged veins)", "value": "varicocele"}, {"label": "Hydrocele or fluid around testicle", "value": "hydrocele"}, {"label": "Other", "value": "other"}, {"label": "I don't remember", "value": "dont_remember"}], "tableView": true, "customConditional": "show = data.testicular_investigated_prior === 'yes'", "optionsLabelPosition": "right"}, {"key": "testicular_investigated_prior_diagnosis_other_description", "type": "textarea", "input": true, "label": "Please describe the other diagnosis you were given:", "tableView": true, "autoExpand": false, "placeholder": "e.g. testicular cyst, spermatocele, infection, etc.", "customConditional": "show = data.testicular_investigated_prior_diagnosis?.other === true"}, {"key": "heading_bowel_symptoms_with_abdominal_pain", "html": "</br><h4 class='mb-n2'>Changes in Bowel Habits</h4>", "type": "content", "input": false, "label": "Bowel Symptom Check", "customConditional": "show = data.urinary_symptom_overview?.bladder_pain === true || data.urinary_symptom_overview?.side_pain === true"}, {"key": "bowel_symptom_changes", "type": "selectboxes", "input": true, "label": "Have you noticed any of the following bowel changes? Please select all that apply:", "values": [{"label": "More constipation than usual", "value": "constipation"}, {"label": "More diarrhea than usual", "value": "diarrhea"}, {"label": "Dark or black stool (looks like coffee grounds)", "value": "black_stool"}, {"label": "Bright red blood on the toilet paper or in the toilet", "value": "red_blood"}, {"label": "Feeling like I can't fully empty my bowels", "value": "incomplete_bowel"}, {"label": "Other bowel changes", "value": "other"}], "tableView": true, "customConditional": "show = data.urinary_symptom_overview?.bladder_pain === true || data.urinary_symptom_overview?.side_pain === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_bowel_symptom_changes", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_bowel_symptom_changes || _.some(_.values(data.bowel_symptom_changes));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.urinary_symptom_overview?.bladder_pain === true || data.urinary_symptom_overview?.side_pain === true"}, {"key": "bowel_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following bowel symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No bowel symptoms reported:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.bowel_symptom_changes, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');", "customConditional": "show = false"}, {"key": "heading_previous_diagnosis", "html": "</br><h4 class='mb-n2'>Previous Diagnosis</h4>", "type": "content", "input": false, "customConditional": "show = data.urinary_symptom_overview && _.some(_.values(data.urinary_symptom_overview)) && !data.none_of_the_above_urinary_symptom_overview"}, {"key": "previous_similar_symptoms", "type": "radio", "input": true, "label": "Have you ever had similar urinary symptoms in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.urinary_symptom_overview && _.some(_.values(data.urinary_symptom_overview)) && !data.none_of_the_above_urinary_symptom_overview"}, {"key": "previous_diagnosis_list", "type": "selectboxes", "input": true, "label": "What were you previously diagnosed with when you had similar urinary symptoms?", "values": [{"label": "Urinary tract infection (UTI)", "value": "uti"}, {"label": "Kidney infection (pyelonephritis)", "value": "kidney_infection"}, {"label": "Bladder inflammation (interstitial cystitis)", "value": "interstitial_cystitis"}, {"label": "Overactive bladder", "value": "oab"}, {"label": "Kidney stones", "value": "stones"}, {"label": "Prostatitis", "value": "prostatitis", "customConditional": "show = data.sex === 'male'"}, {"label": "I used over-the-counter treatment but didn't get tested", "value": "otc_no_test"}, {"label": "The symptoms went away on their own without seeing a doctor", "value": "self_resolved"}, {"label": "I was told the symptoms were normal or not concerning", "value": "told_normal"}, {"label": "I had testing but never got the results", "value": "no_results"}, {"label": "I don't remember the diagnosis", "value": "dont_remember"}, {"label": "The symptoms happen often and I no longer get them checked", "value": "recurrent_untreated"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.previous_similar_symptoms === 'yes'", "optionsLabelPosition": "right"}, {"key": "current_symptoms_feel_similar", "type": "selectboxes", "input": true, "label": "Do your current urinary symptoms feel similar to any of your past diagnoses?", "values": [{"label": "Urinary tract infection (UTI)", "value": "uti"}, {"label": "Kidney infection", "value": "kidney_infection"}, {"label": "Bladder inflammation (interstitial cystitis)", "value": "interstitial_cystitis"}, {"label": "Overactive bladder", "value": "oab"}, {"label": "Prostatitis", "value": "prostatitis", "customConditional": "show = data.sex === 'male'"}, {"label": "Yeast infection (if female)", "value": "yeast", "customConditional": "show = data.sex === 'female'"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.previous_similar_symptoms === 'yes'", "optionsLabelPosition": "right"}, {"key": "heading_general_symptoms", "html": "<h4 class='mb-n2'>General Symptoms</h4>", "type": "content", "input": false, "label": "General Symptoms", "customConditional": "show = data.urinary_symptom_overview && _.some(_.values(data.urinary_symptom_overview))"}, {"key": "general_symptoms", "type": "selectboxes", "input": true, "label": "Have you had any of the following general symptoms? Please select all that apply:", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Vomiting", "value": "vomiting"}, {"label": "Feeling generally unwell or sick", "value": "unwell"}], "adminFlag": true, "tableView": true, "customConditional": "show = data.urinary_symptom_overview && _.some(_.values(data.urinary_symptom_overview))", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_general_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_general_symptoms || _.some(_.values(data.general_symptoms));"}, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.urinary_symptom_overview && _.some(_.values(data.urinary_symptom_overview))"}, {"key": "recommend_uti", "type": "textfield", "input": true, "label": "recommend_uti:", "hidden": true, "disabled": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = data.urinary_symptom_overview && _.some(_.values(data.urinary_symptom_overview))", "refreshOnChange": true}, {"key": "urinary_idrs", "type": "textfield", "input": true, "label": "urinary Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []", "refreshOnChange": true}]}