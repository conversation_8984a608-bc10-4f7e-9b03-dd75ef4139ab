{"components": [{"tab_name": "symptoms", "intake_template_key": "red-flags"}, {"key": "has_symptoms", "type": "radio", "input": true, "label": "Are you currently experiencing any other physical symptoms?", "inline": false, "values": [{"label": "Yes, I have symptoms not listed above", "value": true}, {"label": "No, I have no symptoms (asymptomatic)", "value": false}], "tab_name": "symptoms", "validate": {"required": true}, "tableView": true, "confirm_label": "Current symptoms:", "optionsLabelPosition": "right"}, {"tab_name": "symptoms", "intake_template_key": "master-symptom-vaginal-hpc"}, {"tab_name": "symptoms", "intake_template_key": "master-symptom-urinary-hpc"}, {"tab_name": "symptoms", "intake_template_key": "master-symptom-genital-rash-hpc"}, {"tab_name": "symptoms", "intake_template_key": "master-symptom-oral-hpc"}, {"key": "any_other_symptoms", "type": "radio", "input": true, "label": "Do you have any other symptoms not covered by the questions above?", "inline": false, "values": [{"label": "Yes, I have additional symptoms not listed above", "value": true}, {"label": "No, all of my symptoms are listed above", "value": false}], "tab_name": "symptoms", "validate": {"required": true}, "tableView": true, "defaultValue": false, "confirm_label": "Other symptoms:", "customConditional": "show = data.has_symptoms===true", "optionsLabelPosition": "right"}, {"key": "stated_other_symptoms", "type": "textarea", "input": true, "label": "Please use this area to describe any symptoms not described above.<br>Please leave this section blank if you are asymptomatic or have no symptoms.", "tab_name": "symptoms", "adminFlag": true, "tableView": true, "autoExpand": false, "placeholder": "No other relevant concerns", "customConditional": "show = data.show_all && data.any_other_symptoms === true;"}, {"key": "onset_partner_sti", "type": "radio", "input": true, "label": "Did your symptoms start after a new sexual partner, or are you concerned about partner infidelity (i.e. your partner might have other partners)?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't understand this question", "value": "did_not_understand"}], "tab_name": "symptoms", "validate": {"required": true}, "tableView": true, "confirm_label": "Symptoms started after new partner:", "customConditional": "show = data.sku && _.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti'], data.sku);", "optionsLabelPosition": "right"}, {"key": "sti_testing_requested", "type": "radio", "input": true, "label": "Would you like to add STI/STD testing to your screening today?", "inline": false, "values": [{"label": "Yes, I'd like to add STI testing.", "value": true}, {"label": "No, I do not want to add STI testing.", "value": false}], "tab_name": "symptoms", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku && _.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti'], data.sku) && data.onset_partner_sti != false", "confirm_label": "Add STI testing:", "optionsLabelPosition": "right"}, {"key": "purpose_of_consultation", "html": "<h2 class='text-center'><strong>Purpose of Consultation</strong></h2>", "type": "content", "input": false, "label": "Purpose of Consultation", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "testing_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for testing or treatment:", "values": [{"label": "New Partner", "value": "new_partner_exposure"}, {"label": "Concern About Partner Infidelity ", "value": "concern_about_partner_infidelity"}, {"label": "No Testing Since My Last Partner", "value": "no_testing_since_last_partner"}, {"label": "Partner Notifed Me of A Positive Result", "value": "notified_of_a_positive_result"}, {"label": "Open Relationship/Lifestyle", "value": "open_relationship"}, {"label": "My Last Test Was Done Before The End of My Window Period", "value": "early_test_window_period"}, {"label": "Never had STI Testing", "value": "never_had_testing"}, {"label": "Adult Film / Sex Work & No Testing Since Last Exposure", "value": "adult_film_sex_work_no_testing"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for testing:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "stated_reason_for_testing", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for testing:", "adminFlag": true, "tableView": true, "autoExpand": false, "confirm_label": "Other reason(s) for testing:", "customConditional": "show = data.show_all && data.testing_indication && data.testing_indication.other;"}, {"key": "content6", "html": "<h2 style=\"text-align:center;\"><strong>Intake History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "last_sti_test", "type": "radio", "input": true, "label": "When was your last test for STIs outside of using TeleTest?", "inline": false, "values": [{"label": "Never", "value": "never"}, {"label": "1-14 days ago", "value": "1-14_days"}, {"label": "15-30 days ago", "value": "15-30_days"}, {"label": "31-90 days ago", "value": "31-90_days"}, {"label": "90+ days ago", "value": "90+_days"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "tableView": true, "confirm_label": "Last STI test:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "past_std", "type": "radio", "input": true, "label": "Have you tested positive or been treated for syphilis, HIV, or Hepatitis C in the past?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "Doesn't apply to me as I haven't been tested before", "value": "N/A"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past STIs:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "heading_syphilis_history", "html": "</br><h4>Syphilis</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON><PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.past_std == true"}, {"key": "previous_syphilis_positive", "type": "radio", "input": true, "label": "Have you tested positive for syphilis in the past?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "Doesn't apply to me as I haven't been tested before", "value": "N/A"}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous syphilis positive:", "customConditional": "show = data.past_std == true", "optionsLabelPosition": "right"}, {"key": "previous_syphilis_treatment", "type": "radio", "input": true, "label": "Have you been treated for syphilis in the past?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't understand this question", "value": "did_not_understand"}, {"label": "I don't know if syphilis is what I was treated for", "value": "did_not_know_if_treated_for_syphilis"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous syphilis treatment:", "customConditional": "show = data.previous_syphilis_positive == true", "optionsLabelPosition": "right"}, {"key": "syphilis_last_treatment", "type": "radio", "input": true, "label": "What was your last treatment for syphilis?", "inline": false, "values": [{"label": "An injection of penicillin once", "value": "early_syphilis_single_dose"}, {"label": "An injection of penicillin once every week for three weeks", "value": "latent_syphilis_three_doses"}, {"label": "Two weeks of doxycycline", "value": "early_syphilis_doxycycline_2_weeks"}, {"label": "Four weeks of doxycycline", "value": "latent_syphilis_doxycycline_4_weeks"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Last syphilis treatment:", "customConditional": "show = data.previous_syphilis_treatment == true", "optionsLabelPosition": "right"}, {"key": "syphilis_treatment_location", "type": "radio", "input": true, "label": "Where were you treated for syphilis?", "inline": false, "values": [{"label": "Canada - within Ontario", "value": "canada_ontario"}, {"label": "Canada - outside of Ontario", "value": "canada_outside_ontario"}, {"label": "The United States", "value": "united_states"}, {"label": "A different country", "value": "different_country"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Syphilis treatment location:", "customConditional": "show = data.previous_syphilis_treatment == true", "optionsLabelPosition": "right"}, {"key": "last_rpr_titre_level", "data": {"values": [{"label": "I have no idea what this is", "value": "did_not_know"}, {"label": "I don't remember", "value": "did_not_remember"}, {"label": "1:1", "value": "1_1"}, {"label": "1:2", "value": "1_2"}, {"label": "1:4", "value": "1_4"}, {"label": "1:8", "value": "1_8"}, {"label": "1:16", "value": "1_16"}, {"label": "1:32", "value": "1_32"}, {"label": "1:64", "value": "1_64"}, {"label": "1:128", "value": "1_128"}, {"label": "1:256", "value": "1_256"}]}, "type": "select", "input": true, "label": "What was your last syphilis titre level?", "widget": "html5", "dataSrc": "values", "validate": {"required": true}, "tableView": true, "confirm_label": "Last syphilis titre level:", "customConditional": "show = !!data.syphilis_treatment_location;", "optionsLabelPosition": "right"}, {"key": "last_rpr_test", "data": {"values": [{"label": "I have no idea", "value": "did_not_know"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4-6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "12-24 months ago", "value": "12-24_months"}, {"label": "24+ months ago", "value": "24+_months"}, {"label": "Never had one", "value": "never_had"}]}, "type": "select", "input": true, "label": "When was your last titre test?", "widget": "html5", "tooltip": "An RPR test measures the levels of syphilis antibodies in your blood.", "validate": {"required": true}, "tableView": true, "confirm_label": "Last titre test:", "customConditional": "show = !!data.last_rpr_titre_level;", "optionsLabelPosition": "right"}, {"key": "heading_hiv_history", "html": "<h4>HIV</h4>", "type": "content", "input": false, "label": "HIV Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.past_std == true"}, {"key": "previous_hiv_positive", "type": "radio", "input": true, "label": "Have you tested positive for HIV in the past?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous HIV positive:", "customConditional": "show = data.past_std == true", "optionsLabelPosition": "right"}, {"key": "unprotected_sex", "type": "radio", "input": true, "label": "Have you had vaginal or anal sex without protection (i.e. condom) within the last 3 months?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Unprotected sex within 3 months:", "customConditional": "show = data.show_all", "optionsLabelPosition": "right"}, {"key": "number_of_recent_sexual_partners", "type": "radio", "input": true, "label": "How many sexual partners have you had within the last 6 months?", "inline": false, "values": [{"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "1", "value": "1"}, {"label": "2-3", "value": "2-3"}, {"label": "4-9", "value": "4-9"}, {"label": "10+", "value": "10+"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Number of sexual partners within last 6 months:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "partner_sex", "type": "selectboxes", "input": true, "label": "Please select the sex(s) of your sexual partners:", "values": [{"label": "Male", "value": "male"}, {"label": "Female", "value": "female"}, {"label": "Trans", "value": "trans"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Partner sex(s):", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "type_of_sex", "type": "selectboxes", "input": true, "label": "Please select your route of exposure (i.e. type of sex) including both protected and unprotected sex. This helps the physician know what medical tests are appropriate for your care.", "values": [{"label": "Vaginal Sex", "value": "vaginal"}, {"label": "Perform Anal Sex (ie your penis in partner's anus)", "value": "perform_anal", "customConditional": "show = data.sex != 'female';"}, {"label": "Receive Anal Sex (partner's penis in your anus)", "value": "receive_anal", "customConditional": "show = data.partner_sex && (data.partner_sex.male || data.partner_sex.trans || data.partner_sex.prefer_not_to_disclose);"}, {"label": "Perform Oral Sex (ie your mouth)", "value": "perform_oral"}, {"label": "Receive Oral Sex (ie partner's mouth)", "value": "receive_oral"}, {"label": "Use sex toys", "value": "sex_toys"}, {"label": "No sexual contact except for fingering/digital penetration", "value": "digital_penetration"}, {"label": "No sexual contact", "value": "no_sex"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Route of exposure:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "opt_out_only_oral_anal", "type": "radio", "input": true, "label": "<p>Based on your route of exposure (ie type of sex), standard urine testing won't identify an infection. We recommend obtaining site-specific (ie anal/rectal or throat) swab testing through a local clinic. However, you may still pursue urine testing.</p>Please select an option:", "values": [{"label": "I understand, and do NOT want to pursue urine testing", "value": "no_testing"}, {"label": "I understand, and still want to pursue urine testing", "value": "keep_testing"}, {"label": "I do not understand", "value": "did_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Urine testing:", "customConditional": "show = data.type_of_sex && !data.type_of_sex.vaginal && !data.type_of_sex.receive_oral && (data.type_of_sex.perform_oral || data.type_of_sex.receive_anal);", "optionsLabelPosition": "right"}, {"key": "heading_exposure_history", "html": "<h4>Exposure History</h4>", "type": "content", "input": false, "tableView": false, "refreshOnChange": true, "customConditional": "show = data.show_all && !!data.type_of_sex;"}, {"key": "concerned_about_known_exposure", "type": "radio", "input": true, "label": "Have you recently had sexual contact with someone who was confirmed positive for an STI by a healthcare provider or lab test?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Concerned about known exposure:", "customConditional": "show = data.show_all && !!data.type_of_sex;", "optionsLabelPosition": "right"}, {"key": "type_of_exposure", "type": "radio", "input": true, "label": "What type of exposure have you had?", "inline": false, "values": [{"label": "Received anonymous text message", "value": "anonymous_text"}, {"label": "Current partner tested positive", "value": "current_partner_positive"}, {"label": "Prior partner tested positive", "value": "prior_partner_positive"}, {"label": "Current partner suspects an STI but doesn't have test results", "value": "current_partner_suspects"}, {"label": "I haven't had an exposure that has been confirmed by a physician or tests", "value": "no_confirmed_exposure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Type of exposure:", "customConditional": "show = data.concerned_about_known_exposure == true;", "optionsLabelPosition": "right"}, {"key": "list_of_confirmed_exposures", "type": "selectboxes", "input": true, "label": "Please report any confirmed exposures (i.e. partner notified you of a confirmed or suspected infection they had):", "values": [{"label": "Chlamydia", "value": "chlamydia"}, {"label": "Gonorrhea", "value": "gonorrhea"}, {"label": "Trichomonas", "value": "trichomonas"}, {"label": "HIV", "value": "hiv"}, {"label": "Syphilis", "value": "syphilis"}, {"label": "Herpes", "value": "herpes"}, {"label": "Other", "value": "other"}, {"label": "I wasn't notified what STI I was exposed to", "value": "wasnt_notified_of_which_sti"}, {"label": "Doesn't apply to me", "value": "N/A"}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "inputType": "checkbox", "confirm_label": "Confirmed exposures:", "tableView": true, "customConditional": "show = data.show_all && !!data.type_of_exposure && data.concerned_about_known_exposure != false;", "optionsLabelPosition": "right"}, {"key": "other_sti_exposure_reason", "type": "textarea", "input": true, "label": "Please list your concerns about “other” STI exposures here:", "tableView": true, "autoExpand": false, "confirm_label": "Other exposure reason:", "customConditional": "show = data.list_of_confirmed_exposures && !!data.type_of_exposure && data.list_of_confirmed_exposures.other;"}, {"key": "heading_chlamydia_gonorrhea_trichomonas", "html": "<h2><strong>Information about Chlamydia, Gonorrhea, or Trichomonas</strong></h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.list_of_confirmed_exposures && (data.list_of_confirmed_exposures.chlamydia || data.list_of_confirmed_exposures.gonorrhea || data.list_of_confirmed_exposures.trichomonas);"}, {"key": "confirm_all_three_stis", "type": "radio", "input": true, "confirm_label": "Confirmed all three STIs:", "label": "Are you sure your partner tested positive for all three STIs (chlamydia, gonorrhea, and trichomonas)?", "inline": false, "values": [{"label": "Yes, they had lab testing confirming they were positive for all three", "value": true}, {"label": "No, they didn't have lab testing but I'm concerned about these STIs", "value": false}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.list_of_confirmed_exposures && data.list_of_confirmed_exposures.chlamydia && data.list_of_confirmed_exposures.gonorrhea && data.list_of_confirmed_exposures.trichomonas && !data.list_of_confirmed_exposures.hiv && !data.list_of_confirmed_exposures.syphilis && !data.list_of_confirmed_exposures.herpes && !data.list_of_confirmed_exposures.other;", "optionsLabelPosition": "right"}, {"key": "last_exposure_time", "type": "radio", "input": true, "confirm_label": "Last exposure time:", "label": "How long ago was your last exposure to this individual?", "inline": false, "values": [{"label": "< 72 hours", "value": "<72_hours"}, {"label": "3-14 days", "value": "3-14_days"}, {"label": "15-30 days", "value": "15-30_days"}, {"label": "31-90 days", "value": "31-90_days"}, {"label": "91-180 days", "value": "91-180_days"}, {"label": "Over 6 months ago", "value": "over_6_months_ago"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.list_of_confirmed_exposures && (data.list_of_confirmed_exposures.chlamydia || data.list_of_confirmed_exposures.gonorrhea || data.list_of_confirmed_exposures.trichomonas || data.list_of_confirmed_exposures.syphilis) && !data.list_of_confirmed_exposures.hiv;", "optionsLabelPosition": "right"}, {"key": "heading_hiv", "html": "<h2><strong>HIV Related Testing</strong></h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !!data.list_of_confirmed_exposures && data.list_of_confirmed_exposures.hiv;"}, {"key": "confirm_hiv_positive", "type": "radio", "input": true, "confirm_label": "Confirmed HIV positive:", "label": "Are you sure your partner tested positive for HIV?", "inline": false, "values": [{"label": "Yes, they had lab testing confirming they were positive for HIV", "value": "yes_confirmed_hiv"}, {"label": "No, they didn't have lab testing but I'm concerned about HIV", "value": "no_concerned_but_not_tested_hiv"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.list_of_confirmed_exposures && data.list_of_confirmed_exposures.hiv;", "optionsLabelPosition": "right"}, {"key": "hiv_last_exposure", "type": "radio", "confirm_label": "Last exposure time:", "input": true, "label": "How long ago was your last exposure to this individual?", "inline": false, "values": [{"label": "< 72 hours", "value": "<72_hours"}, {"label": "3-18 days", "value": "3-18_days"}, {"label": "18-44 days", "value": "18-44_days"}, {"label": "44-90 days", "value": "44-90_days"}, {"label": "90-180 days", "value": "90-180_days"}, {"label": "180-365 days", "value": "180-365_days"}, {"label": "1+ year ago", "value": "1+year_ago"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.confirm_hiv_positive == 'yes_confirmed_hiv'", "optionsLabelPosition": "right"}, {"key": "pep_taken", "type": "radio", "confirm_label": "PEP taken:", "input": true, "label": "Have you taken post-exposure prophylaxis (PEP)? PEP is a medication that can help prevent HIV infection after being exposed. It is only effective if obtained within 72 hours of exposure and requires an emergency room visit.", "inline": false, "values": [{"label": "Yes, I have taken PEP", "value": "yes_pep"}, {"label": "No, I haven't taken PEP", "value": "no_pep"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.hiv_last_exposure == '<72_hours';", "optionsLabelPosition": "right"}, {"key": "heading_syphilis", "html": "<h2><strong>Syphilis Related Testing</strong></h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !!data.list_of_confirmed_exposures && data.list_of_confirmed_exposures.syphilis;"}, {"key": "confirm_syphilis_positive", "type": "radio", "input": true, "confirm_label": "Confirmed syphilis positive:", "label": "Are you sure your partner tested positive for syphilis?", "inline": false, "values": [{"label": "Yes, they had lab testing confirming they were positive for syphilis", "value": "yes_confirmed_syphilis"}, {"label": "No, they didn't have lab testing but I'm concerned about syphilis", "value": "concerned_but_not_tested_syphilis"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.list_of_confirmed_exposures && data.list_of_confirmed_exposures.syphilis;", "optionsLabelPosition": "right"}, {"key": "syphilis_symptoms", "type": "selectboxes", "input": true, "confirm_label": "Syphilis symptoms:", "label": "Have you had any symptoms of syphilis?", "values": [{"label": "Sore or ulcer", "value": "sore_or_ulcer"}, {"label": "Rash on the body, often on the palms of the hands or soles of the feet", "value": "rash"}, {"label": "Fever", "value": "fever"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.confirm_syphilis_positive == 'yes_confirmed_syphilis';", "optionsLabelPosition": "right"}, {"key": "no_syphilis_symptoms", "type": "checkbox", "confirm_label": "No syphilis symptoms:", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_syphilis_symptoms || !!_.some(_.values(data.syphilis_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.confirm_syphilis_positive == 'yes_confirmed_syphilis';"}, {"key": "pregnancy_heading", "html": "<h4>Pregnancy</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.show_all && data.sex == 'female';"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "inline": false, "confirm_label": "Currently pregnant:", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't know", "value": "i_dont_know"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "vaginal_sex_without_contraception", "type": "radio", "confirm_label": "Unprotected vaginal sex:", "input": true, "label": "Have you had unprotected vaginal sex with a male partner in the last 5 days, and are not on any form of contraception (i.e. Birth  Control Pills, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IUD, tubes tied, partner has vasectomy)?", "inline": false, "values": [{"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && data.unprotected_sex == true && (['1-7_days', '8-14_days', '15-30_days'].indexOf(data.last_sex) > -1);", "optionsLabelPosition": "right"}, {"key": "pregnancy_advice", "html": "<h2>Pregnancy Advice</h2><p>Having unprotected sex with a male partner without a condom can put you at risk of pregnancy.</p><p><strong>Plan B,</strong> a medication to prevent unwanted pregnancy after recent sexual contact, is effective <strong>up to 5 days</strong> after unprotected vaginal sex.</p><p>If you have questions about Plan B, please bring discuss it with your physician at the time of your appointment.</p>", "type": "content", "input": false, "label": "Content", "tableView": true, "refreshOnChange": true, "customConditional": "show = data.vaginal_sex_without_contraception == true;"}, {"intake_template_key": "window-period"}, {"tab_name": "preferences", "intake_template_key": "hpc-sti-preferences"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat((_.map(_.filter(_.flatMap(['chief_complaint'],k=>['other'].map(v=>[k,v])),kv=>data[kv[0]]===kv[1]),kv=>kv[0]+'.'+kv[1])),(data.chief_complaint!='testing'&&data.first_prefer=='chat'?['first_prefer.chat']:[]),(['fem_pn_uti', 'std_pn_uti'].includes(data.sku)?['sku.uti']:[]),(_.flatMap({'type_of_sex':['did_not_understand']},(vs,k)=>{return _.map(_.filter(vs, v=>data[k]&&data[k][v]), v=>k+'.'+v)})),(_.map(_.filter(['std_red_flag_symptoms'],k=>_.some(_.values(data[k]))),k=>k+'.some')),(data.sku=='std_pn_cgtvh'&&data.chief_complaint!='treatment_only'&&_.keys(_.pickBy(data.userBulletKeys)).length < 2 ? ['less_than_2_assays_selected'] : []),)", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-mm':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:_.concat(['appointment-intake','edit-intake'],(!data.rxts?.length?['get-req']:['get-req-rx']))"}]}