{"components": [{"key": "semen_instructions_header", "html": "<h2 class='text-center'>Semen Analysis Intake Form</h2><p>Please complete the following questionnaire to help us better understand your needs for semen analysis testing.</p>", "type": "content", "input": false, "label": "Instructions", "tableView": false, "refreshOnChange": false}, {"key": "semen_testing_indication_header", "html": "<h3>Reason for Testing</h3>", "type": "content", "input": false, "label": "Testing Indications", "tableView": false, "refreshOnChange": false}, {"key": "vasectomy_testing", "type": "radio", "input": true, "label": "Are you undergoing semen testing to check if a previous vasectomy was successful?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Vasectomy testing:", "optionsLabelPosition": "right"}, {"key": "testing_indications", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for undergoing a semen analysis:", "values": [{"label": "Required for sperm donation", "value": "required_for_donation"}, {"label": "Have been trying to have a baby without success", "value": "unsuccessful_attempts"}, {"label": "Follow-up on a previous abnormal result", "value": "follow_up_abnormal"}, {"label": "Testing early to help plan starting a family", "value": "fertility_planning"}, {"label": "My partner had a miscarriage or stillbirth", "value": "miscarriage_stillbirth"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Testing indications:", "customConditional": "show = data.vasectomy_testing == 'no'", "optionsLabelPosition": "right"}, {"key": "other_reason", "type": "textarea", "input": true, "label": "Please specify other reasons:", "tableView": true, "confirm_label": "Other reasons for testing:", "customConditional": "show = data.vasectomy_testing == 'no' && data.testing_indications.other"}, {"key": "children_before", "type": "radio", "input": true, "label": "Have you had children before?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Children before testing:", "customConditional": "show = data.vasectomy_testing == 'no' && data.testing_indications && (data.testing_indications.unsuccessful_attempts || data.testing_indications.follow_up_abnormal || data.testing_indications.fertility_planning)", "optionsLabelPosition": "right"}, {"key": "children_number", "type": "radio", "input": true, "label": "How many children do you have?", "values": [{"label": "0", "value": "0"}, {"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5+", "value": "5_plus"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Number of children:", "customConditional": "show = !!data.children_before && data.children_before == 'yes'", "optionsLabelPosition": "right"}, {"key": "children_ages_conceived", "type": "selectboxes", "input": true, "label": "How old were you when each of your children was born?", "values": [{"label": "<20", "value": "under 20"}, {"label": "20-24", "value": "20-24"}, {"label": "25-29", "value": "25-29"}, {"label": "30-34", "value": "30-34"}, {"label": "35-39", "value": "35-39"}, {"label": "40-44", "value": "40-44"}, {"label": "45-49", "value": "45-49"}, {"label": "50-54", "value": "50-54"}, {"label": "55-59", "value": "55-59"}, {"label": ">60", "value": "over 60"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Ages when children were conceived:", "customConditional": "show = !!data.children_number"}, {"key": "assisted_technologies_used", "type": "selectboxes", "input": true, "label": "Did you require any of the following fertility treatments in the past to have children?", "values": [{"label": "In Vitro Fertilization (IVF)", "value": "ivf"}, {"label": "Intracytoplasmic Sperm Injection (ICSI)", "value": "icsi"}, {"label": "Intrauterine Insemination (IUI)", "value": "iui"}], "tableView": true, "confirm_label": "Fertility treatments used:", "customConditional": "show = !!data.children_number"}, {"key": "no_assisted_technologies_used", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a fertility treatment."}, "validate": {"custom": "valid = !!data.no_assisted_technologies_used || !!_.some(_.values(data.assisted_technologies_used));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!data.children_number"}, {"key": "children_partner", "type": "radio", "input": true, "label": "Were these children with your current partner or a previous partner?", "values": [{"label": "All with a previous partner", "value": "all_previous_partner"}, {"label": "Some with a previous partner and some with my current partner", "value": "some_with_both"}, {"label": "All with current partner", "value": "all_current_partner"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Children with current or previous partner:", "customConditional": "show = !!data.children_before && data.children_before == 'yes'", "optionsLabelPosition": "right"}, {"key": "current_relationship_header", "html": "<h3></br>Current Relationship and Efforts</h3>", "type": "content", "input": false, "label": "Current Relationship and Efforts", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.vasectomy_testing === 'no' && !data.testing_indications?.required_for_donation"}, {"key": "active_with_partner", "type": "radio", "input": true, "label": "Are you currently active, or have you been active in the past with a partner you wish to have children with?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Active with partner:", "customConditional": "show = data.vasectomy_testing === 'no' && !data.testing_indications?.required_for_donation"}, {"key": "partner_age", "data": {"values": [{"label": "Doesn't apply to me", "value": "not_applicable"}, {"label": "I don't have a partner", "value": "no_partner"}, {"label": "< 30 years", "value": "under_30"}, {"label": "30 years", "value": "30"}, {"label": "31 years", "value": "31"}, {"label": "32 years", "value": "32"}, {"label": "33 years", "value": "33"}, {"label": "34 years", "value": "34"}, {"label": "35 years", "value": "35"}, {"label": "36 years", "value": "36"}, {"label": "37 years", "value": "37"}, {"label": "38 years", "value": "38"}, {"label": "39 years", "value": "39"}, {"label": "40 years", "value": "40"}, {"label": "41-42 years", "value": "41_42"}, {"label": "43-44 years", "value": "43_44"}, {"label": "45+ years", "value": "45_plus"}]}, "type": "select", "input": true, "label": "How old is your partner?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Partner's age:", "customConditional": "show = data.vasectomy_testing === 'no' && data.active_with_partner === 'yes'"}, {"key": "partner_children_before", "type": "radio", "input": true, "label": "Has your partner had children before?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Doesn't Apply To Me", "value": "doesn't_apply"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Partner's children before testing:", "customConditional": "show = data.vasectomy_testing === 'no' && data.active_with_partner === 'yes'"}, {"key": "partner_children_number", "data": {"values": [{"label": "None", "value": "0"}, {"label": "1 child", "value": "1"}, {"label": "2 children", "value": "2"}, {"label": "3 children", "value": "3"}, {"label": "4 children", "value": "4"}, {"label": "5 or more children", "value": "5_more"}]}, "type": "select", "input": true, "label": "How many children does your partner have?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Partner's number of children:", "customConditional": "show = data.vasectomy_testing === 'no' && data.active_with_partner === 'yes' && data.partner_children_before === 'yes'"}, {"key": "partner_last_child_age", "data": {"values": [{"label": "I don't know", "value": "don't_know"}, {"label": "Under 20 years", "value": "under 20"}, {"label": "20-21 years", "value": "20-21"}, {"label": "22-23 years", "value": "22-23"}, {"label": "24-25 years", "value": "24-25"}, {"label": "26-27 years", "value": "26-27"}, {"label": "28-29 years", "value": "28-29"}, {"label": "30-31 years", "value": "30-31"}, {"label": "32-33 years", "value": "32-33"}, {"label": "34-35 years", "value": "34-35"}, {"label": "36-37 years", "value": "36-37"}, {"label": "38-39 years", "value": "38-39"}, {"label": "40-41 years", "value": "40-41"}, {"label": "42-43 years", "value": "42-43"}, {"label": "44-45 years", "value": "44-45"}, {"label": "46 years or older", "value": "46-plus"}]}, "type": "select", "input": true, "label": "How old was your partner when she last had children?", "widget": "html5", "tableView": true, "confirm_label": "Partner's age at last child:", "customConditional": "show = data.vasectomy_testing === 'no' && data.active_with_partner === 'yes' && !!data.partner_children_number"}, {"key": "partner_fertility_evaluation", "type": "radio", "input": true, "label": "Has your partner ever had fertility investigations (i.e. an ultrasound, bloodwork)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Partner completed previous fertility evaluations:", "customConditional": "show = data.vasectomy_testing === 'no' && data.active_with_partner === 'yes' && !!data.partner_children_number"}, {"key": "partner_fertility_tests", "type": "selectboxes", "input": true, "label": "What fertility investigations has your partner undergone?", "values": [{"label": "Ultrasound", "value": "ultrasound"}, {"label": "Bloodwork", "value": "bloodwork"}, {"label": "Hysterosalpingogram (HSG)", "value": "hsg"}, {"label": "Laparoscopy", "value": "laparoscopy"}, {"label": "Other (please specify below)", "value": "other"}], "tableView": true, "confirm_label": "Partner's fertility investigations:", "customConditional": "show = data.partner_fertility_evaluation === 'yes'"}, {"key": "partner_fertility_tests_other", "type": "textarea", "input": true, "label": "Please specify other fertility investigations:", "tableView": true, "confirm_label": "Other fertility investigations specified:", "customConditional": "show = data.partner_fertility_evaluation === 'yes' && data.partner_fertility_tests.other"}, {"key": "unprotected_sex_duration", "data": {"values": [{"label": "We haven't had sex yet", "value": "no_sex"}, {"label": "Less than 3 months", "value": "<3"}, {"label": "3-5 months", "value": "3_5"}, {"label": "6-12 months", "value": "6_12"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_2_years"}]}, "type": "select", "input": true, "label": "How long have you been trying to have unprotected sex on a regular basis? (in months)", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Duration of unprotected intercourse:", "customConditional": "show = data.vasectomy_testing === 'no' && data.active_with_partner === 'yes'"}, {"key": "partner_in_same_country", "type": "radio", "input": true, "label": "Is your partner in the same country as you?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Partner in same country:", "customConditional": "show = data.vasectomy_testing === 'no' && data.active_with_partner === 'yes' && !!data.unprotected_sex_duration"}, {"key": "frequency_of_intercourse", "data": {"values": [{"label": "Multiple times a week", "value": "multiple_times_week"}, {"label": "Weekly", "value": "weekly"}, {"label": "Less than once a week", "value": "less_than_weekly"}]}, "type": "select", "input": true, "label": "How frequently do you engage in intercourse?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Frequency of intercourse:", "customConditional": "show = data.vasectomy_testing === 'no' && data.active_with_partner === 'yes' && !!data.unprotected_sex_duration"}, {"key": "previous_semen_analysis_header", "html": "<h3></br>Previous Semen Analysis</h3>", "type": "content", "input": false, "label": "Previous Semen Analysis", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.vasectomy_testing == 'no'"}, {"key": "previous_semen_analysis", "type": "radio", "input": true, "label": "Have you completed semen analysis testing before?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous semen analysis completed:", "customConditional": "show = data.vasectomy_testing == 'no'", "optionsLabelPosition": "right"}, {"key": "last_semen_analysis_date", "data": {"values": [{"label": "< 3 months ago", "value": "less than 3 months"}, {"label": "3 - 6 months ago", "value": "3-6 months"}, {"label": "6 - 9 months ago", "value": "6-9 months"}, {"label": "9 - 12 months ago", "value": "9-12 months"}, {"label": "12 - 15 months ago", "value": "12-15 months"}, {"label": "15 - 18 months ago", "value": "15-18 months"}, {"label": "> 18 months ago", "value": "greater than 18 months"}]}, "type": "select", "input": true, "label": "When was the last semen analysis performed?", "widget": "html5", "tooltip": "Select the approximate time frame when you had your last semen analysis.", "tableView": true, "confirm_label": "Last semen analysis date:", "customConditional": "show = data.vasectomy_testing == 'no' && (!!data.previous_semen_analysis && data.previous_semen_analysis == 'yes')", "optionsLabelPosition": "right"}, {"key": "semen_analysis_abnormalities", "type": "radio", "input": true, "label": "Was your previous semen count normal or abnormal?", "values": [{"label": "Normal", "value": "normal"}, {"label": "Abnormal", "value": "abnormal"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous semen analysis abnormalities:", "customConditional": "show = data.vasectomy_testing == 'no' && !!data.last_semen_analysis_date"}, {"key": "specific_abnormalities", "type": "selectboxes", "input": true, "label": "Do you remember what was abnormal on your prior testing?", "values": [{"label": "Low sperm count", "value": "low_sperm_count"}, {"label": "Abnormal semen morphology", "value": "abnormal_morphology"}, {"label": "Low motility", "value": "low_motility"}, {"label": "Other (please specify below)", "value": "other"}, {"label": "I don't remember", "value": "doesnt_remember"}], "tableView": true, "confirm_label": "Specific abnormalities from previous semen analysis:", "customConditional": "show = data.vasectomy_testing == 'no' && data.semen_analysis_abnormalities === 'abnormal'", "optionsLabelPosition": "right"}, {"key": "other_abnormalities_specify", "type": "textarea", "input": true, "label": "Please specify other abnormalities:", "tableView": true, "confirm_label": "Other abnormalities specified:", "customConditional": "show = data.semen_analysis_abnormalities == 'abnormal' && data.specific_abnormalities.other"}, {"key": "sperm_count_ranges", "data": {"values": [{"label": "I don't know", "value": "doesn't_know"}, {"label": "< 5 million/mL", "value": "less than 5 million"}, {"label": "5-10 million/mL", "value": "5-10 million"}, {"label": "10-15 million/mL", "value": "10-15 million"}, {"label": "15-20 million/mL", "value": "15-20 million"}, {"label": "> 20 million/mL", "value": "more than 20 million"}]}, "type": "select", "input": true, "label": "Select the range for sperm count:", "widget": "html5", "tableView": true, "confirm_label": "Sperm count range:", "customConditional": "show = !!data.specific_abnormalities && data.specific_abnormalities.low_sperm_count", "optionsLabelPosition": "right"}, {"key": "morphology_ranges", "data": {"values": [{"label": "I don't know", "value": "don't know"}, {"label": "Less than 4%", "value": "less than 4%"}, {"label": "4% to 9%", "value": "4-9%"}, {"label": "10% to 14%", "value": "10-14%"}, {"label": "15% and above", "value": "15%+"}]}, "type": "select", "input": true, "label": "Select the percentage of sperm with normal morphology:", "widget": "html5", "tableView": true, "confirm_label": "Sperm morphology range:", "customConditional": "show = !!data.specific_abnormalities && data.specific_abnormalities.abnormal_morphology", "optionsLabelPosition": "right"}, {"key": "motility_ranges", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "< 10%", "value": "less than 10%"}, {"label": "10-20%", "value": "10-20%"}, {"label": "20-30%", "value": "20-30%"}, {"label": "30-40%", "value": "30-40%"}, {"label": "40-50%", "value": "40-50%"}, {"label": "50-60%", "value": "50-60%"}, {"label": "> 60%", "value": "more than 60%"}]}, "type": "select", "input": true, "label": "Select the percentage of <strong> total </strong> motile sperm:", "widget": "html5", "tableView": true, "confirm_label": "Sperm motility range:", "customConditional": "show = !!data.specific_abnormalities && data.specific_abnormalities.low_motility", "optionsLabelPosition": "right"}, {"key": "risk_factors_header", "html": "<h3>Medical Conditions and Exposures</h3>", "type": "content", "input": false, "label": "Risk Factors and Environmental Exposure", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.vasectomy_testing == 'no'"}, {"key": "medical_conditions_affecting_semen_quality", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with or treated for any of the following conditions:", "values": [{"label": "Varicocele", "value": "varicocele"}, {"label": "Undescended Testicle", "value": "undescended_testicle"}, {"label": "Testicular Torsion (testicle twisted on itself)", "value": "testicular_torsion"}, {"label": "Radiation treatment for cancer", "value": "radiation_treatment"}, {"label": "Groin Her<PERSON>", "value": "groin_surgery"}], "tableView": true, "confirm_label": "Medical conditions affecting semen quality:", "customConditional": "show = data.vasectomy_testing == 'no'", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_medical_conditions", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_medical_conditions || !!_.some(_.values(data.medical_conditions_affecting_semen_quality));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.vasectomy_testing == 'no' && !!data.medical_conditions_affecting_semen_quality"}, {"key": "medical_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following medical conditions affecting semen quality:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.medical_conditions_affecting_semen_quality, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "varicocele_heading", "html": "<h4>Varicocele Details</h4>", "type": "content", "input": false, "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.varicocele"}, {"key": "varicocele_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less than 6 months"}, {"label": "6 months to 1 year", "value": "between 6 months and 1 year"}, {"label": "1-2 years", "value": "1-2 years"}, {"label": "2-5 years", "value": "2-5 years"}, {"label": "More than 5 years", "value": "more than 5 years"}, {"label": "I don't know", "value": "dont_know"}]}, "type": "select", "input": true, "label": "How long have you had a diagnosis of varicocele?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Varicocele duration:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.varicocele"}, {"key": "varicocele_diagnosed_by", "type": "radio", "input": true, "label": "Who diagnosed you with varicocele?", "values": [{"label": "Family Doctor", "value": "family doctor"}, {"label": "Nurse Practitioner", "value": "nurse practitoner"}, {"label": "Urologist", "value": "urologist"}, {"label": "Other specialist", "value": "other specialist"}, {"label": "I don't know", "value": "dont know"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Varicocele diagnosed by:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.varicocele"}, {"key": "varicocele_ultrasound", "type": "radio", "input": true, "label": "Did you have a testicular ultrasound to confirm the diagnosis?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't know", "value": "dont know"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Varicocele ultrasound:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.varicocele"}, {"key": "varicocele_treatment", "type": "radio", "input": true, "label": "Have you received treatment for varicocele?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Varicocele treatment:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.varicocele"}, {"key": "varicocele_treatment_type", "type": "selectboxes", "input": true, "label": "What type of treatment have you received?", "values": [{"label": "Surgery (e.g., varicocelectomy)", "value": "surgery"}, {"label": "Embolization", "value": "embolization"}, {"label": "Watchful waiting (no immediate treatment)", "value": "watchful_waiting"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Varicocele treatment type:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.varicocele && data.varicocele_treatment === 'yes'"}, {"key": "undescended_testicle_heading", "html": "<h4>Undescended Testicle Details</h4>", "type": "content", "input": false, "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.undescended_testicle"}, {"key": "undescended_testicle_side", "type": "radio", "input": true, "label": "Which side was affected?", "values": [{"label": "Left", "value": "left"}, {"label": "Right", "value": "right"}, {"label": "Both", "value": "both"}], "tableView": true, "confirm_label": "Undescended testicle side:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.undescended_testicle"}, {"key": "undescended_testicle_age", "data": {"values": [{"label": "Infancy (under 1 year)", "value": "infancy"}, {"label": "1-5 years", "value": "1-5 years ago"}, {"label": "6-10 years", "value": "6-10 years ago"}, {"label": "Older than 10 years", "value": "older than 10 years"}, {"label": "Never corrected", "value": "never_corrected"}]}, "type": "select", "input": true, "label": "At what age was this condition corrected?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Undescended testicle age:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.undescended_testicle"}, {"key": "undescended_testicle_treatment_type", "type": "selectboxes", "input": true, "label": "What type of treatment was performed?", "values": [{"label": "Surgery", "value": "surgery"}, {"label": "Hormonal therapy", "value": "hormonal_therapy"}, {"label": "Other", "value": "other"}, {"label": "No treatment", "value": "no_treatment"}], "tableView": true, "confirm_label": "Undescended testicle treatment type:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.undescended_testicle"}, {"key": "testicular_torsion_heading", "html": "<h4>Testicular Torsion Details</h4>", "type": "content", "input": false, "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.testicular_torsion"}, {"key": "testicular_torsion_age", "data": {"values": [{"label": "Childhood (under 10 years)", "value": "childhood"}, {"label": "Adolescence (10-18 years)", "value": "adolescence"}, {"label": "Adulthood (over 18)", "value": "adulthood"}]}, "type": "select", "input": true, "label": "At what age did the torsion occur?", "widget": "html5", "tableView": true, "confirm_label": "Testicular torsion age:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.testicular_torsion"}, {"key": "testicular_torsion_treatment", "type": "radio", "input": true, "label": "Was the affected testicle removed?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Testicular torsion treatment:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.testicular_torsion"}, {"key": "radiation_treatment_heading", "html": "<h4>Radiation Treatment for Cancer Details</h4>", "type": "content", "input": false, "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.radiation_treatment"}, {"key": "radiation_treatment_location", "data": {"values": [{"label": "Testicular area", "value": "testicular_area"}, {"label": "Pelvic area", "value": "pelvic_area"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Other area", "value": "other_area"}]}, "type": "select", "input": true, "label": "What area of the body received radiation treatment?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Radiation treatment location:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.radiation_treatment"}, {"key": "radiation_treatment_duration", "data": {"values": [{"label": "Less than 1 year ago", "value": "less_1_year"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "2-5 years ago", "value": "2_5_years"}, {"label": "More than 5 years ago", "value": "more_5_years"}, {"label": "I don't know", "value": "dont_know"}]}, "type": "select", "input": true, "label": "How long ago was the radiation treatment performed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Radiation treatment duration:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.radiation_treatment"}, {"key": "groin_surgery_heading", "html": "<h4>Groin Hernia Surgery Details</h4>", "type": "content", "input": false, "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.groin_surgery"}, {"key": "groin_surgery_side", "type": "radio", "input": true, "label": "Which side was the hernia on?", "values": [{"label": "Left", "value": "left"}, {"label": "Right", "value": "right"}, {"label": "Both", "value": "both"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Groin hernia side:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.groin_surgery"}, {"key": "groin_surgery_type", "data": {"values": [{"label": "Open surgery", "value": "open_surgery"}, {"label": "Laparoscopic surgery", "value": "laparoscopic_surgery"}, {"label": "I don't know", "value": "dont_know"}]}, "type": "select", "input": true, "label": "What type of hernia surgery did you have?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Groin hernia surgery type:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.groin_surgery"}, {"key": "groin_surgery_complications", "type": "selectboxes", "input": true, "label": "Did you experience any of the following complications?", "values": [{"label": "Chronic pain", "value": "chronic_pain"}, {"label": "Nerve damage", "value": "nerve_damage"}, {"label": "Testicular atrophy", "value": "testicular_atrophy"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Groin surgery complications:", "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.groin_surgery"}, {"key": "groin_surgery_complications_other", "type": "textarea", "input": true, "label": "Please specify other complications:", "tableView": true, "customConditional": "show = data.vasectomy_testing === 'no' && data.medical_conditions_affecting_semen_quality.groin_surgery && data.groin_surgery_complications.other"}, {"key": "environmental_conditions_affecting_fertility", "type": "selectboxes", "input": true, "label": "Are you exposed to any of the following environmental factors that might affect fertility?", "values": [{"label": "Exposure to toxic chemicals (e.g., pesticides, heavy metals)", "value": "toxic_chemicals"}, {"label": "Frequent exposure to high temperatures (e.g., saunas, hot tubs)", "value": "high_temperatures"}, {"label": "Extended periods of sedentary behavior (e.g., long hours sitting)", "value": "sedentary_behavior"}, {"label": "Regular handling of overheating electronic devices (e.g., laptops on lap)", "value": "overheating_devices"}], "tableView": true, "confirm_label": "Environmental conditions affecting fertility:", "customConditional": "show = data.vasectomy_testing == 'no'", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_environmental_conditions", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_environmental_conditions || !!_.some(_.values(data.environmental_conditions_affecting_fertility));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.vasectomy_testing == 'no'"}, {"key": "environmental_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following environmental factors affecting fertility:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.environmental_conditions_affecting_fertility, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "toxic_chemicals_exposure", "data": {"values": [{"label": "Pesticides", "value": "pesticides"}, {"label": "Heavy metals (e.g., lead, mercury)", "value": "heavy_metals"}, {"label": "Industrial chemicals (e.g., solvents)", "value": "industrial_chemicals"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What type of toxic chemicals are you exposed to?", "widget": "html5", "confirm_label": "Toxic chemicals exposure:", "customConditional": "show = data.vasectomy_testing == 'no' && data.environmental_conditions_affecting_fertility.toxic_chemicals"}, {"key": "high_temperatures_exposure", "data": {"values": [{"label": "Saunas", "value": "saunas"}, {"label": "Hot tubs", "value": "hot_tubs"}, {"label": "Industrial heat sources (e.g., ovens, furnaces)", "value": "industrial_heat_sources"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What type of high temperature exposure are you experiencing?", "widget": "html5", "confirm_label": "High temperatures exposure:", "customConditional": "show = data.vasectomy_testing == 'no' && data.environmental_conditions_affecting_fertility.high_temperatures"}, {"key": "sedentary_behavior_exposure", "data": {"values": [{"label": "Prolonged sitting (e.g., desk work)", "value": "prolonged_sitting"}, {"label": "Driving for long hours", "value": "long_driving"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What type of sedentary behavior are you experiencing?", "widget": "html5", "confirm_label": "Sedentary behavior exposure:", "customConditional": "show = data.vasectomy_testing == 'no' && data.environmental_conditions_affecting_fertility.sedentary_behavior"}, {"key": "overheating_devices_exposure", "data": {"values": [{"label": "Laptops on lap", "value": "laptops_on_lap"}, {"label": "Mobile devices generating heat", "value": "mobile_devices_heat"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What type of overheating devices are you exposed to?", "widget": "html5", "confirm_label": "Overheating devices exposure:", "customConditional": "show = data.vasectomy_testing == 'no' && data.environmental_conditions_affecting_fertility.overheating_devices"}, {"key": "fertility_specialist_header", "html": "<h3>Fertility Referral</h3>", "type": "content", "input": false, "label": "Fertility Specialist Interaction", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.vasectomy_testing == 'no'"}, {"key": "fertility_specialist_referral", "type": "radio", "input": true, "label": "Have you sought a referral from a fertility specialist?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fertility specialist referral:", "customConditional": "show = data.vasectomy_testing == 'no'"}, {"key": "preliminary_assessments", "type": "radio", "input": true, "label": "Have you been assessed by the fertility specialist?", "values": [{"label": "Yes, I've been assessed", "value": "yes"}, {"label": "No, I'm waiting for an assessment", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preliminary assessments by fertility specialist:", "customConditional": "show = data.vasectomy_testing == 'no' && data.fertility_specialist_referral === 'yes'"}, {"key": "fertility_specialist_assessment_date", "data": {"values": [{"label": "Less than 6 months ago", "value": "less_6_months"}, {"label": "6 months to 1 year ago", "value": "6_months_1_year"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "More than 2 years ago", "value": "more_2_years"}, {"label": "I don't know", "value": "dont_know"}]}, "type": "select", "input": true, "label": "When were you assessed by the fertility specialist?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Fertility specialist assessment date:", "customConditional": "show = data.vasectomy_testing == 'no' && data.fertility_specialist_referral === 'yes' && data.preliminary_assessments === 'yes'"}, {"key": "fertility_investigations", "type": "selectboxes", "input": true, "label": "Have you undergone any fertility investigations?", "values": [{"label": "Semen analysis", "value": "semen_analysis"}, {"label": "Bloodwork (e.g., hormonal testing)", "value": "bloodwork"}, {"label": "Testicular ultrasound", "value": "testicular_ultrasound"}, {"label": "Genetic testing", "value": "genetic_testing"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Fertility investigations:", "customConditional": "show = data.vasectomy_testing === 'no' && data.fertility_specialist_referral === 'yes' && data.preliminary_assessments === 'yes'"}, {"key": "fertility_investigations_other", "type": "textarea", "input": true, "label": "Please specify other investigations:", "tableView": true, "confirm_label": "Fertility investigations (other):", "customConditional": "show = data.vasectomy_testing === 'no' && data.fertility_specialist_referral === 'yes' && data.preliminary_assessments === 'yes' && data.fertility_investigations.other"}, {"key": "fertility_treatments", "type": "selectboxes", "input": true, "label": "Have you pursued any fertility treatments?", "values": [{"label": "In Vitro Fertilization (IVF)", "value": "ivf"}, {"label": "Intrauterine Insemination (IUI)", "value": "iui"}, {"label": "Surgical procedures", "value": "surgery"}, {"label": "Hormonal therapy", "value": "hormonal_therapy"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Fertility treatments:", "customConditional": "show = data.vasectomy_testing === 'no' && data.fertility_specialist_referral === 'yes' && data.preliminary_assessments === 'yes'"}, {"key": "fertility_treatments_other", "type": "textarea", "input": true, "label": "Please specify other treatments:", "tableView": true, "confirm_label": "Fertility treatments (other):", "customConditional": "show = data.vasectomy_testing === 'no' && data.fertility_specialist_referral === 'yes' && data.preliminary_assessments === 'yes' && data.fertility_treatments.other"}, {"key": "medication_use_header", "html": "<h3>Medications</h3>", "type": "content", "input": false, "label": "Medication Use", "tableView": false, "confirmlabel": "Medication use:", "refreshOnChange": false, "customConditional": "show = data.vasectomy_testing == 'no'"}, {"key": "performance_drugs_use", "type": "radio", "input": true, "label": "Are you currently using or have you ever used performance-enhancing drugs?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Performance-enhancing drugs use:", "customConditional": "show = data.vasectomy_testing == 'no'"}, {"key": "trt_use_current", "type": "radio", "input": true, "label": "Are you currently on testosterone replacement therapy (TRT)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "TRT use (current):", "customConditional": "show = data.vasectomy_testing == 'no' && !!data.performance_drugs_use && (data.performance_drugs_use === 'yes' || data.performance_drugs_use === 'no' || data.performance_drugs_use === 'prefer_not_to_disclose')"}, {"key": "trt_use_past", "type": "radio", "input": true, "label": "Were you previously on testosterone replacement therapy (TRT)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "TRT use (past):", "customConditional": "show = data.vasectomy_testing == 'no' && data.trt_use_current === 'no'"}, {"key": "vasectomy_header", "html": "<h3></br>Vasectomy Details</h3>", "type": "content", "input": false, "label": "Vasectomy Details", "tableView": false, "customConditional": "show = data.vasectomy_testing == 'yes'"}, {"key": "vasectomy_completion_time", "type": "radio", "input": true, "label": "When was your vasectomy completed?", "values": [{"label": "1-3 months ago", "value": "1_3_months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6 or more months ago", "value": "6_plus_months"}], "tableView": true, "confirm_label": "Vasectomy completion time:", "customConditional": "show = data.vasectomy_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "semen_analysis_post_vasectomy", "type": "radio", "input": true, "label": "Have you had a semen analysis after the vasectomy?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Semen analysis post-vasectomy:", "customConditional": "show = !!data.vasectomy_completion_time", "optionsLabelPosition": "right"}, {"key": "reason_for_testing_post_vasectomy", "type": "selectboxes", "input": true, "label": "If you previously cleared the 3 month window for testing and had a sperm-free sample, repeat semen analysis is not routinely recommended due to generally low failure rates.</br></br><li>Early failure rate (3-6 months post-vasectomy): 0.2%-0.5% (2/1,000-5/1,000).</li><li>Late failure rate ( 0.04-0.08% (4/10,000-8/10,000) </li></br>Please select from the following reasons why you wanted to repeat testing:", "values": [{"label": "Partner requires confirmation", "value": "partner_confirmation"}, {"label": "I want to be certain my vasectomy is still working", "value": "certainty_vasectomy_working"}, {"label": "My current partner is pregnant", "value": "partner_pregnant"}, {"label": "I'm considering a reversal", "value": "considering_reversal"}, {"label": "I don't understand this question", "value": "doesn't_understand"}], "tableView": true, "confirm_label": "Reason for testing post-vasectomy:", "customConditional": "show = data.semen_analysis_post_vasectomy === 'yes'", "optionsLabelPosition": "right"}, {"key": "userBulletKeys", "type": "textfield", "input": true, "label": "User Bullets:", "hidden": true, "disabled": true, "multiple": true, "hideLabel": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = data.vasectomy_testing == 'yes' ? ['SEMEN-VASECTOMY'] : ['SEMEN-FERTILITY']", "optionsLabelPosition": "right"}, {"intake_template_key": "hx-any-questions"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value=_.concat((data.any_other_questions === true && !!data.stated_other_questions && !/^\\s*$/.test(data.stated_other_questions)?['stated_other_questions.not_blank']:[]));", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-semen':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}