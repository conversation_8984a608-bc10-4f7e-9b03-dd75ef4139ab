{"components": [{"key": "heading_main_vitamin_d", "html": "<h1><center><strong>Vitamin D Prescription & Testing</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_medical_indication", "html": "<h4>Indication for Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consultation_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for your consultation:", "values": [{"label": "Test Vitamin D levels", "value": "test_vitamin_d_levels", "shortcut": ""}, {"label": "Obtain Precription for Vitamin D", "value": "prescription_vitamin_d", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for testing:", "optionsLabelPosition": "right"}, {"key": "consultation_indication_other", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for vitamin D assessment:", "tableView": true, "autoExpand": false, "customConditional": "show = data.consultation_indication.other;"}, {"key": "heading_testing", "html": "<h3>Previous Vitamin D Testing&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_vit_d_test", "type": "radio", "input": true, "label": "Have you ever had your Vitamin D levels tested in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_vit_d_test", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "When was your last Vitamin D test outside of TeleTest completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_vit_d_test == true;", "optionsLabelPosition": "right"}, {"key": "last_vit_d_results", "type": "radio", "input": true, "label": "What was your last vitamin D level on your last set of bloodwork?", "inline": false, "values": [{"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}, {"label": "< 30 nmol/L (12 ng/mL) ", "value": "<30 nmol/L", "shortcut": ""}, {"label": "30-50 nmol/L (12-20 ng/mL)", "value": "30-50_nmol/L", "shortcut": ""}, {"label": "50-75 nmol/L (20-30 ng/mL)", "value": "50-75_nmol/L", "shortcut": ""}, {"label": "75-125 nmol/L (30-50 ng/mL)", "value": "75-125_nmol/L", "shortcut": ""}, {"label": "125-375 nmol/L (50-150 ng/mL)", "value": "125_nmol/L", "shortcut": ""}, {"label": "375+ nmol/L (150+ ng/mL)", "value": "375_nmol/L+", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_vit_d_test == true;", "optionsLabelPosition": "right"}, {"key": "stable_vit_d_bloodwork", "type": "radio", "input": true, "label": "Have you had stable Vitamin D values over several blood tests?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't know", "value": "don't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_vit_d_test == true;", "optionsLabelPosition": "right"}, {"key": "heading_medication", "html": "<h3>Vitamin D Supplements&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "currently_on_meds", "type": "radio", "input": true, "label": "Are you currently on Vitamin D supplements?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_vit_d_medication", "type": "radio", "input": true, "label": "Please select which of the following medication you are currently on:", "inline": false, "values": [{"label": "Vitamin D2 (ergocalciferol) <strong>Pills</strong>", "value": "d2-pills", "shortcut": ""}, {"label": "Vitamin D2 (ergocalciferol) <strong>Drops</strong>", "value": "d2-drops", "shortcut": ""}, {"label": "Vitamin D3 (cholecalciferol) <strong>Pills</strong>", "value": "d3-pills", "shortcut": ""}, {"label": "Vitamin D3 (cholecalciferol) <strong>Drops</strong>", "value": "d3-drops", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "other_medication", "type": "textarea", "input": true, "label": "Please state your “other” medication:", "tableView": true, "autoExpand": false, "customConditional": "show = data.current_vit_d_medication == 'other';"}, {"key": "previously_on_meds", "type": "radio", "input": true, "label": "Were you previously on Vitamin D supplements?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "past_vit_d_medication", "type": "radio", "input": true, "label": "Please select which of the following medication you were previously on:", "inline": false, "values": [{"label": "Vitamin D2 (ergocalciferol) <strong>Pills</strong>", "value": "d2-pills", "shortcut": ""}, {"label": "Vitamin D2 (ergocalciferol) <strong>Drops</strong>", "value": "d2-drops", "shortcut": ""}, {"label": "Vitamin D3 (cholecalciferol) <strong>Pills</strong>", "value": "d3-pills", "shortcut": ""}, {"label": "Vitamin D3 (cholecalciferol) <strong>Drops</strong>", "value": "d3-drops", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "last_vit_d_test", "data": {"values": [{"label": "<1 week ago", "value": "less_1_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4_weeks-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "When were you last on Vitamin D supplements?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_vit_d_test == true;", "optionsLabelPosition": "right"}, {"key": "current_vit_d_dosing", "data": {"values": [{"label": "400 IU (10 mcg) daily", "value": "400_iu"}, {"label": "600 IU (15 mcg) daily", "value": "600_iu"}, {"label": "800 IU (20 mcg) daily", "value": "800_iu"}, {"label": "1000 IU (25 mcg) daily", "value": "1000_iu"}, {"label": "2000 IU (50 mcg) daily", "value": "2000_iu"}, {"label": "2500 IU (62.5 mcg) daily", "value": "2500_iu"}, {"label": "4000 IU (100 mcg) daily", "value": "4000_iu"}, {"label": "5000 IU (125 mcg) daily", "value": "5000_iu"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "Please specify your Vitamin D dose:", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_meds == true || data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "current_dose_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your dose below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.current_vit_d_dosing == 'other';"}, {"key": "duration_current_dose", "type": "radio", "input": true, "label": "How long have you been taking your current daily dose?", "inline": false, "values": [{"label": "< 6 weeks", "value": "<6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3_months-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12+ months", "value": "12+_months"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "last_time_took_medication", "type": "radio", "input": true, "label": "When were you last on Vitamin D supplements?", "inline": false, "values": [{"label": "< 6 weeks", "value": "<6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3_months-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12 months", "value": "12+_months"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "reason_stopping", "type": "radio", "input": true, "label": "Please select your reason(s) for stopping your Vitamin D supplement:", "values": [{"label": "I ran out of medication", "value": "ran_out_of_medication", "shortcut": ""}, {"label": "Didn't have anyone to monitor my Vitamin D levels", "value": "no_one_monitor_tsh", "shortcut": ""}, {"label": "Had a normal vitamin D level after running out of pills", "value": "normal_tsh_after_cessation", "shortcut": ""}, {"label": "I wasn't sure I needed them anymore", "value": "felt_didn't_need_them", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previously_on_meds == true;", "confirm_label": "Reason(s) for stopping:", "optionsLabelPosition": "right"}, {"key": "stopped_med_other", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for stopping your Vitamin D:", "tableView": true, "autoExpand": false, "customConditional": "show = data.reason_stopping == 'other';"}, {"key": "heading_compliance", "html": "<h3>Medication Compliance&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.currently_on_meds == true || data.previously_on_meds == true;", "tableView": false, "refreshOnChange": false}, {"key": "current_medication_consistency", "type": "radio", "input": true, "label": "When you take vitamin D, how would you describe how consistent you are with your medication:", "inline": false, "values": [{"label": "I take my medication consistently and <strong>never</strong> miss doses.", "value": "consistent_usage", "shortcut": ""}, {"label": "I take my medication consistently but <strong>sometimes</strong> miss doses.", "value": "regularly_misses_doses", "shortcut": ""}, {"label": "I don't take my medication consistently.", "value": "inconsistent_usage", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_meds == true || data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "heading_low_vit_d_rf", "html": "<h3>Vitamin D Risk Factors&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hx_low_vit_d", "type": "radio", "input": true, "label": "Do you have a history of low vitamin D levels in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_conditions_predisposing_low_vit_d", "html": "<h4>Relevant Medical History&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "greater_2000_iu_vitamin_d_daily", "type": "radio", "input": true, "label": "Do you consume (on average) more than 2000 IU of Vitamin D daily?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vit_d_risk_medical_conditions", "type": "selectboxes", "input": true, "label": "Have you been diagnosed by a physician with any of the following medical conditions:", "inline": false, "values": [{"label": "Osteoperosis/osteopenia (Requires previous Bone Mineral Density Test)", "value": "osteoperosis/osteopenia", "shortcut": ""}, {"label": "Rickets", "value": "rickets", "shortcut": ""}, {"label": "Celiac disease", "value": "celiac_disease", "shortcut": ""}, {"label": "Gluten Intolerance", "value": "gluten_intolerance", "shortcut": ""}, {"label": "Cystic Fibrosis", "value": "cystic_fibrosis", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>'s Disease", "value": "crohns", "shortcut": ""}, {"label": "Chronic Kidney Disease (eGFR < 30)", "value": "ckd_egfr<30", "shortcut": ""}, {"label": "Nephrotic syndrome", "value": "nephrotic_syndrome", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vit_d_risk_medical_conditions", "type": "selectboxes", "input": true, "label": "Have you had any of the following medical procedures:", "inline": false, "values": [{"label": "Roux-en-Y gastric bypass", "value": "roux_en_y", "shortcut": ""}, {"label": "adjustable gastric banding", "value": "adjustable_banding", "shortcut": ""}, {"label": "Gastrectomy", "value": "gastrectomy", "shortcut": ""}, {"label": "Removal of part of your small bowel (small intestine)", "value": "small_bowel_resection", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vit_d_risk_medications", "type": "selectboxes", "input": true, "label": "Are you on any of the following medications:", "inline": false, "values": [{"label": "Cholestyramine <strong>(<PERSON>: <PERSON><PERSON>)</strong>", "value": "cholestyramine", "shortcut": ""}, {"label": "Colestipol <strong>(Brand: <PERSON><PERSON><PERSON>)</strong>", "value": "colestipol", "shortcut": ""}, {"label": "Orlistat <strong>(Brand: <PERSON><PERSON><PERSON>)</strong>", "value": "orlistat", "shortcut": ""}, {"label": "Any of the following seizure medication: phenytoin, fosphenytoin, primidone", "value": "seizure_medication", "shortcut": ""}, {"label": "Daily use of mineral oil, senokot, bisadodyl", "value": "laxative_daily_use", "shortcut": ""}, {"label": "Daily use of prednisone", "value": "mineral_oil", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "fitzpatrick_type", "type": "radio", "input": true, "label": "Please select your skin color (lightest to darkest), based on your <a href=\"https://www.arpansa.gov.au/sites/default/files/legacy/pubs/RadiationProtection/FitzpatrickSkinType.pdf\" target='_blank'>Fitzpatrick Skin Type</a>", "inline": false, "values": [{"label": "Type 1: <PERSON><PERSON>", "value": "type_1", "shortcut": ""}, {"label": "Type 2: White Skin", "value": "type_2", "shortcut": ""}, {"label": "Type 3: Light Brown Skin", "value": "type_3", "shortcut": ""}, {"label": "Type 4: Moderate Brown Skin", "value": "type_4", "shortcut": ""}, {"label": "Type 5: <PERSON>", "value": "type_5", "shortcut": ""}, {"label": "Type 6: Deeply Pigmented Dark Brown to Black Skin", "value": "type_6", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "clothing_religious_garb", "type": "radio", "input": true, "label": "Do you wear clothing that covers most of your skin for religous or cultural reasons (ie. head scarf, zostikon, niqab, habit, etc)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}]}